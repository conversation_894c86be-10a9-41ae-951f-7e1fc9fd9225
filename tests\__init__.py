"""
Comprehensive Testing Suite for Enhanced AI Agent
"""

import sys
import os
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Test configuration
TEST_CONFIG = {
    'workspace_dir': project_root / 'test_workspace',
    'temp_dir': project_root / 'test_temp',
    'fixtures_dir': project_root / 'tests' / 'fixtures',
    'timeout': 30,
    'verbose': True
}

# Ensure test directories exist
for dir_path in [TEST_CONFIG['workspace_dir'], TEST_CONFIG['temp_dir'], TEST_CONFIG['fixtures_dir']]:
    dir_path.mkdir(exist_ok=True)
