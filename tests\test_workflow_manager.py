"""
Comprehensive tests for the Intelligent Workflow Manager
"""

import unittest
import tempfile
import shutil
import json
from pathlib import Path
from unittest.mock import Mock, patch

import sys
sys.path.append(str(Path(__file__).parent.parent))

from core.intelligent_workflow_manager import (
    IntelligentWorkflowManager, WorkflowState, WorkflowStep, 
    WorkflowPlan, ConversationContext, Priority
)
from tests import TEST_CONFIG


class TestIntelligentWorkflowManager(unittest.TestCase):
    """Test suite for Intelligent Workflow Manager."""
    
    def setUp(self):
        """Set up test environment."""
        self.test_workspace = TEST_CONFIG['workspace_dir'] / 'workflow_test'
        self.test_workspace.mkdir(exist_ok=True)
        self.workflow_manager = IntelligentWorkflowManager(self.test_workspace)
    
    def tearDown(self):
        """Clean up test environment."""
        if self.test_workspace.exists():
            shutil.rmtree(self.test_workspace, ignore_errors=True)
    
    def test_workflow_manager_initialization(self):
        """Test workflow manager initialization."""
        self.assertIsNotNone(self.workflow_manager)
        self.assertEqual(self.workflow_manager.workspace_dir, self.test_workspace)
        self.assertIsNotNone(self.workflow_manager.active_workflows)
        self.assertIsNotNone(self.workflow_manager.conversation_contexts)
    
    def test_conversation_context_creation(self):
        """Test conversation context creation."""
        user_input = "Create a Python web application with authentication"
        context = self.workflow_manager.create_conversation_context(user_input)
        
        self.assertIsNotNone(context)
        self.assertEqual(context.user_intent, user_input)
        self.assertIsNotNone(context.conversation_id)
        self.assertIsNotNone(context.domain)
        self.assertGreater(context.complexity_level, 0)
    
    def test_domain_analysis(self):
        """Test domain analysis functionality."""
        test_cases = [
            ("Create a React website", "web_development"),
            ("Analyze data with pandas", "data_analysis"),
            ("Deploy with Docker", "devops"),
            ("Train a machine learning model", "machine_learning"),
            ("Build a mobile app", "mobile_development"),
            ("Set up database queries", "database"),
            ("Write unit tests", "testing"),
            ("Implement authentication", "security")
        ]
        
        for user_input, expected_domain in test_cases:
            domain = self.workflow_manager._analyze_domain(user_input)
            self.assertEqual(domain, expected_domain, 
                           f"Failed for input: '{user_input}', expected: {expected_domain}, got: {domain}")
    
    def test_complexity_estimation(self):
        """Test complexity estimation."""
        test_cases = [
            ("Create a simple hello world", 2),
            ("Build a web application", 5),
            ("Implement advanced machine learning pipeline", 8),
            ("Create distributed microservices architecture", 10)
        ]
        
        for user_input, min_expected_complexity in test_cases:
            complexity = self.workflow_manager._estimate_complexity(user_input)
            self.assertGreaterEqual(complexity, min_expected_complexity,
                                  f"Complexity too low for: '{user_input}'")
            self.assertLessEqual(complexity, 10, "Complexity should not exceed 10")
    
    def test_workflow_plan_generation(self):
        """Test workflow plan generation."""
        user_input = "Create a Python Flask API"
        context = self.workflow_manager.create_conversation_context(user_input)
        available_tools = ['create_project', 'file_write', 'shell_execute']
        
        plan = self.workflow_manager.generate_workflow_plan(context, available_tools)
        
        self.assertIsNotNone(plan)
        self.assertIsNotNone(plan.plan_id)
        self.assertGreater(len(plan.steps), 0)
        self.assertEqual(plan.status, WorkflowState.IDLE)
    
    def test_workflow_step_execution(self):
        """Test workflow step execution."""
        # Create a mock tool executor
        def mock_tool_executor(tool_name, parameters):
            return f"Executed {tool_name} with {parameters}"
        
        # Create a test step
        step = WorkflowStep(
            step_id="test_step",
            name="Test Step",
            description="A test step",
            tool_name="test_tool",
            parameters={"param1": "value1"}
        )
        
        # Execute the step
        success = self.workflow_manager.execute_workflow_step(step, mock_tool_executor)
        
        self.assertTrue(success)
        self.assertEqual(step.status, WorkflowState.COMPLETED)
        self.assertIsNotNone(step.result)
        self.assertIsNotNone(step.actual_duration)
    
    def test_workflow_step_error_handling(self):
        """Test workflow step error handling."""
        # Create a mock tool executor that raises an exception
        def failing_tool_executor(tool_name, parameters):
            raise Exception("Tool execution failed")
        
        # Create a test step
        step = WorkflowStep(
            step_id="failing_step",
            name="Failing Step",
            description="A step that will fail",
            tool_name="failing_tool",
            parameters={}
        )
        
        # Execute the step
        success = self.workflow_manager.execute_workflow_step(step, failing_tool_executor)
        
        self.assertFalse(success)
        self.assertEqual(step.status, WorkflowState.ERROR)
        self.assertIsNotNone(step.error_message)
    
    def test_next_step_prediction(self):
        """Test next step prediction."""
        user_input = "Create a web application"
        context = self.workflow_manager.create_conversation_context(user_input)
        
        predictions = self.workflow_manager.predict_next_steps(context)
        
        self.assertIsInstance(predictions, list)
        self.assertGreater(len(predictions), 0)
        
        # Check if predictions are relevant to web development
        prediction_text = ' '.join(predictions).lower()
        self.assertTrue(any(keyword in prediction_text for keyword in 
                          ['project', 'structure', 'component', 'setup']))
    
    def test_workflow_progress_tracking(self):
        """Test workflow progress tracking."""
        # Create a workflow plan
        user_input = "Create a simple project"
        context = self.workflow_manager.create_conversation_context(user_input)
        plan = self.workflow_manager.generate_workflow_plan(context, ['create_project'])
        
        # Get progress
        progress = self.workflow_manager.get_workflow_progress(plan.plan_id)
        
        self.assertIsNotNone(progress)
        self.assertIn('plan_id', progress)
        self.assertIn('progress_percentage', progress)
        self.assertIn('total_steps', progress)
        self.assertEqual(progress['plan_id'], plan.plan_id)
    
    def test_optimization_suggestions(self):
        """Test optimization suggestions."""
        # Create a workflow plan with some steps
        user_input = "Create a complex project"
        context = self.workflow_manager.create_conversation_context(user_input)
        plan = self.workflow_manager.generate_workflow_plan(context, ['create_project', 'file_write'])
        
        # Get optimization suggestions
        suggestions = self.workflow_manager.suggest_optimizations(plan.plan_id)
        
        self.assertIsInstance(suggestions, list)
        # Suggestions might be empty for a new plan, which is fine
    
    def test_conversation_insights(self):
        """Test conversation insights generation."""
        user_input = "Create multiple projects"
        context = self.workflow_manager.create_conversation_context(user_input)
        
        # Add some conversation history
        context.conversation_history.extend([
            {'role': 'user', 'content': 'First message', 'domain': 'web_development', 'complexity': 5},
            {'role': 'assistant', 'content': 'Response', 'domain': 'web_development', 'complexity': 5},
            {'role': 'user', 'content': 'Second message', 'domain': 'data_analysis', 'complexity': 7}
        ])
        
        insights = self.workflow_manager.get_conversation_insights(context.conversation_id)
        
        self.assertIsNotNone(insights)
        self.assertIn('conversation_id', insights)
        self.assertIn('message_count', insights)
        self.assertIn('average_complexity', insights)
        self.assertIn('domain_distribution', insights)
    
    def test_pattern_similarity_calculation(self):
        """Test pattern similarity calculation."""
        # Create test steps
        step1 = WorkflowStep(
            step_id="step1",
            name="Create Project",
            description="Create a Python project",
            tool_name="create_project",
            parameters={"template": "python", "name": "test"}
        )
        
        pattern_data = {
            "tool_name": "create_project",
            "parameters": {"template": "python", "type": "web"},
            "context": "Create a Python web project"
        }
        
        similarity = self.workflow_manager._calculate_similarity(step1, pattern_data)
        
        self.assertGreater(similarity, 0)
        self.assertLessEqual(similarity, 1.0)
    
    def test_error_recovery(self):
        """Test error recovery mechanisms."""
        # Create a failed step
        step = WorkflowStep(
            step_id="failed_step",
            name="Failed Step",
            description="A step that failed",
            tool_name="test_tool",
            parameters={"timeout": 10}
        )
        step.status = WorkflowState.ERROR
        step.error_message = "timeout: Operation timed out"
        
        # Attempt recovery
        recovery_step = self.workflow_manager.recover_from_error(step)
        
        if recovery_step:  # Recovery might not always be possible
            self.assertIsNotNone(recovery_step)
            self.assertEqual(recovery_step.priority, Priority.HIGH)
            self.assertGreater(recovery_step.parameters.get('timeout', 0), step.parameters['timeout'])


class TestWorkflowComponents(unittest.TestCase):
    """Test individual workflow components."""
    
    def test_workflow_step_creation(self):
        """Test workflow step creation."""
        step = WorkflowStep(
            step_id="test_step",
            name="Test Step",
            description="A test step",
            tool_name="test_tool",
            parameters={"param": "value"}
        )
        
        self.assertEqual(step.step_id, "test_step")
        self.assertEqual(step.name, "Test Step")
        self.assertEqual(step.status, WorkflowState.IDLE)
        self.assertEqual(step.retry_count, 0)
        self.assertEqual(step.max_retries, 3)
    
    def test_workflow_plan_creation(self):
        """Test workflow plan creation."""
        plan = WorkflowPlan(
            plan_id="test_plan",
            name="Test Plan",
            description="A test plan"
        )
        
        self.assertEqual(plan.plan_id, "test_plan")
        self.assertEqual(plan.name, "Test Plan")
        self.assertEqual(plan.status, WorkflowState.IDLE)
        self.assertEqual(len(plan.steps), 0)
    
    def test_conversation_context_creation(self):
        """Test conversation context creation."""
        context = ConversationContext(
            conversation_id="test_conv",
            user_intent="Test intent",
            domain="test_domain",
            complexity_level=5
        )
        
        self.assertEqual(context.conversation_id, "test_conv")
        self.assertEqual(context.user_intent, "Test intent")
        self.assertEqual(context.domain, "test_domain")
        self.assertEqual(context.complexity_level, 5)
        self.assertEqual(len(context.conversation_history), 0)


class TestWorkflowIntegration(unittest.TestCase):
    """Integration tests for workflow management."""
    
    def setUp(self):
        """Set up integration test environment."""
        self.test_workspace = TEST_CONFIG['workspace_dir'] / 'workflow_integration'
        self.test_workspace.mkdir(exist_ok=True)
        self.workflow_manager = IntelligentWorkflowManager(self.test_workspace)
    
    def tearDown(self):
        """Clean up integration test environment."""
        if self.test_workspace.exists():
            shutil.rmtree(self.test_workspace, ignore_errors=True)
    
    def test_complete_workflow_lifecycle(self):
        """Test complete workflow lifecycle."""
        # Step 1: Create conversation context
        user_input = "Create a Python web application with database"
        context = self.workflow_manager.create_conversation_context(user_input)
        
        # Step 2: Generate workflow plan
        available_tools = ['create_project', 'file_write', 'shell_execute', 'generate_dockerfile']
        plan = self.workflow_manager.generate_workflow_plan(context, available_tools)
        
        # Step 3: Check initial progress
        progress = self.workflow_manager.get_workflow_progress(plan.plan_id)
        self.assertEqual(progress['progress_percentage'], 0)
        
        # Step 4: Simulate step execution
        if plan.steps:
            step = plan.steps[0]
            
            def mock_executor(tool_name, params):
                return f"Executed {tool_name}"
            
            success = self.workflow_manager.execute_workflow_step(step, mock_executor)
            self.assertTrue(success)
            
            # Check updated progress
            progress = self.workflow_manager.get_workflow_progress(plan.plan_id)
            self.assertGreater(progress['progress_percentage'], 0)
        
        # Step 5: Get predictions for next steps
        predictions = self.workflow_manager.predict_next_steps(context)
        self.assertGreater(len(predictions), 0)
        
        # Step 6: Get conversation insights
        insights = self.workflow_manager.get_conversation_insights(context.conversation_id)
        self.assertIsNotNone(insights)


if __name__ == '__main__':
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_suite.addTest(unittest.makeSuite(TestIntelligentWorkflowManager))
    test_suite.addTest(unittest.makeSuite(TestWorkflowComponents))
    test_suite.addTest(unittest.makeSuite(TestWorkflowIntegration))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print(f"\nWorkflow Manager Tests Summary:")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\nFailures:")
        for test, traceback in result.failures:
            print(f"- {test}")
    
    if result.errors:
        print("\nErrors:")
        for test, traceback in result.errors:
            print(f"- {test}")
