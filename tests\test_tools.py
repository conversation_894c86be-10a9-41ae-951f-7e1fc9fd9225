"""
Comprehensive tests for all tools in the Enhanced AI Agent
"""

import unittest
import tempfile
import shutil
import json
from pathlib import Path
from unittest.mock import Mock, patch

import sys
sys.path.append(str(Path(__file__).parent.parent))

from tools.enhanced_file_tool import <PERSON>hancedFileTool
from tools.tool_manager import <PERSON><PERSON>Manager, Tool
from tools.advanced_tools import AdvancedFileManager, AdvancedTerminalManager, AdvancedProjectManager
from tests import TEST_CONFIG


class TestEnhancedFileTool(unittest.TestCase):
    """Test suite for Enhanced File Tool."""
    
    def setUp(self):
        """Set up test environment."""
        self.test_workspace = TEST_CONFIG['workspace_dir'] / 'file_tool_test'
        self.test_workspace.mkdir(exist_ok=True)
        self.file_tool = EnhancedFileTool(self.test_workspace)
    
    def tearDown(self):
        """Clean up test environment."""
        if self.test_workspace.exists():
            shutil.rmtree(self.test_workspace, ignore_errors=True)
    
    def test_advanced_grep(self):
        """Test advanced grep functionality."""
        # Create test files
        (self.test_workspace / 'test1.py').write_text('def hello():\n    print("Hello World")')
        (self.test_workspace / 'test2.py').write_text('class World:\n    def greet(self):\n        return "Hello"')
        
        result = self.file_tool.advanced_grep('def', '*.py')
        
        self.assertTrue(result['success'])
        self.assertGreater(result['total_matches'], 0)
        self.assertIn('results', result)
    
    def test_backup_creation(self):
        """Test file backup functionality."""
        test_file = self.test_workspace / 'backup_test.txt'
        test_file.write_text('Original content')
        
        result = self.file_tool.create_backup('backup_test.txt')
        
        self.assertTrue(result['success'])
        self.assertIn('backup_path', result)
        
        # Check if backup file exists
        backup_path = Path(result['backup_path'])
        self.assertTrue(backup_path.exists())
    
    def test_batch_rename(self):
        """Test batch rename functionality."""
        # Create test files
        for i in range(3):
            (self.test_workspace / f'old_name_{i}.txt').write_text(f'Content {i}')
        
        result = self.file_tool.batch_rename('old_name', 'new_name', '*.txt')
        
        self.assertTrue(result['success'])
        self.assertEqual(result['renamed_count'], 3)
        
        # Check if files were renamed
        for i in range(3):
            self.assertTrue((self.test_workspace / f'new_name_{i}.txt').exists())
    
    def test_checksum_calculation(self):
        """Test checksum calculation."""
        test_file = self.test_workspace / 'checksum_test.txt'
        test_file.write_text('Test content for checksum')
        
        result = self.file_tool.calculate_checksums('*.txt')
        
        self.assertTrue(result['success'])
        self.assertGreater(result['file_count'], 0)
        self.assertIn('checksums', result)
    
    def test_project_creation(self):
        """Test project creation from templates."""
        result = self.file_tool.create_project('test_project', 'python')
        
        self.assertTrue(result['success'])
        self.assertEqual(result['project_name'], 'test_project')
        self.assertIn('created_files', result)
        
        # Check if project directory was created
        project_path = self.test_workspace / 'test_project'
        self.assertTrue(project_path.exists())
    
    def test_code_analysis(self):
        """Test code analysis functionality."""
        # Create a test Python file
        test_file = self.test_workspace / 'analysis_test.py'
        test_code = '''
def function1():
    """A simple function."""
    x = 1 + 1
    return x

class TestClass:
    def __init__(self):
        self.value = 42
    
    def method1(self):
        return self.value * 2
'''
        test_file.write_text(test_code)
        
        result = self.file_tool.analyze_code_complexity('analysis_test.py')
        
        self.assertTrue(result['success'])
        self.assertIn('analysis', result)
        
        analysis = result['analysis']
        self.assertGreater(analysis['function_count'], 0)
        self.assertGreater(analysis['class_count'], 0)
    
    def test_code_smell_detection(self):
        """Test code smell detection."""
        # Create a file with potential code smells
        test_file = self.test_workspace / 'smelly_code.py'
        smelly_code = '''
def very_long_function_name_that_exceeds_normal_length_and_should_be_flagged_as_too_long():
    password = "hardcoded_secret_123"  # This should be flagged
    # TODO: Fix this later
    try:
        pass
    except:
        pass
'''
        test_file.write_text(smelly_code)
        
        result = self.file_tool.find_code_smells('smelly_code.py')
        
        self.assertTrue(result['success'])
        self.assertGreater(result['smell_count'], 0)
    
    def test_test_generation(self):
        """Test test template generation."""
        # Create a source file
        source_file = self.test_workspace / 'source.py'
        source_code = '''
def add(a, b):
    return a + b

class Calculator:
    def multiply(self, x, y):
        return x * y
'''
        source_file.write_text(source_code)
        
        result = self.file_tool.generate_test_template('source.py')
        
        self.assertTrue(result['success'])
        self.assertIn('template', result)
        self.assertIn('test_add', result['template'])


class TestToolManager(unittest.TestCase):
    """Test suite for Tool Manager."""
    
    def setUp(self):
        """Set up test environment."""
        self.tool_manager = ToolManager()
    
    def test_tool_registration(self):
        """Test tool registration."""
        def dummy_function():
            return "dummy result"
        
        tool = Tool(
            name="dummy_tool",
            description="A dummy tool for testing",
            function=dummy_function,
            parameters={"type": "object", "properties": {}, "required": []}
        )
        
        self.tool_manager.register_tool(tool)
        
        self.assertIn("dummy_tool", self.tool_manager.tools)
        self.assertEqual(self.tool_manager.tools["dummy_tool"], tool)
    
    def test_tool_execution(self):
        """Test tool execution."""
        def test_function(message: str):
            return f"Hello, {message}!"
        
        tool = Tool(
            name="test_tool",
            description="A test tool",
            function=test_function,
            parameters={
                "type": "object",
                "properties": {
                    "message": {"type": "string"}
                },
                "required": ["message"]
            }
        )
        
        self.tool_manager.register_tool(tool)
        
        # Test execution
        result = self.tool_manager.execute_tool("test_tool", {"message": "World"})
        self.assertEqual(result, "Hello, World!")
    
    def test_tool_listing(self):
        """Test tool listing functionality."""
        # Register a few tools
        for i in range(3):
            tool = Tool(
                name=f"tool_{i}",
                description=f"Tool number {i}",
                function=lambda: f"result_{i}",
                parameters={"type": "object", "properties": {}, "required": []}
            )
            self.tool_manager.register_tool(tool)
        
        tools = self.tool_manager.list_tools()
        self.assertGreaterEqual(len(tools), 3)
    
    def test_tool_validation(self):
        """Test tool parameter validation."""
        def validated_function(required_param: str, optional_param: str = "default"):
            return f"{required_param}-{optional_param}"
        
        tool = Tool(
            name="validated_tool",
            description="A tool with validation",
            function=validated_function,
            parameters={
                "type": "object",
                "properties": {
                    "required_param": {"type": "string"},
                    "optional_param": {"type": "string"}
                },
                "required": ["required_param"]
            }
        )
        
        self.tool_manager.register_tool(tool)
        
        # Test with valid parameters
        result = self.tool_manager.execute_tool("validated_tool", {"required_param": "test"})
        self.assertEqual(result, "test-default")
        
        # Test with missing required parameter should raise an error
        with self.assertRaises(Exception):
            self.tool_manager.execute_tool("validated_tool", {})


class TestAdvancedTools(unittest.TestCase):
    """Test suite for Advanced Tools."""
    
    def setUp(self):
        """Set up test environment."""
        self.test_workspace = TEST_CONFIG['workspace_dir'] / 'advanced_tools_test'
        self.test_workspace.mkdir(exist_ok=True)
        
        self.file_manager = AdvancedFileManager(self.test_workspace)
        self.terminal_manager = AdvancedTerminalManager(self.test_workspace)
        self.project_manager = AdvancedProjectManager(self.test_workspace)
    
    def tearDown(self):
        """Clean up test environment."""
        if self.test_workspace.exists():
            shutil.rmtree(self.test_workspace, ignore_errors=True)
    
    def test_advanced_file_manager(self):
        """Test Advanced File Manager."""
        # Create test files
        (self.test_workspace / 'test1.py').write_text('def hello(): pass')
        (self.test_workspace / 'test2.py').write_text('class World: pass')
        
        # Test advanced grep
        results = self.file_manager.advanced_grep('def', '*.py')
        self.assertGreater(len(results), 0)
        
        # Test backup creation
        backup_path = self.file_manager.create_backup('test1.py')
        self.assertTrue(Path(backup_path).exists())
    
    def test_terminal_manager(self):
        """Test Advanced Terminal Manager."""
        session_id = 'test_session'
        
        # Create session
        success = self.terminal_manager.create_session(session_id)
        self.assertTrue(success)
        
        # Get history (should be empty initially)
        history = self.terminal_manager.get_session_history(session_id)
        self.assertEqual(len(history), 0)
        
        # Close session
        success = self.terminal_manager.close_session(session_id)
        self.assertTrue(success)
    
    def test_project_manager(self):
        """Test Advanced Project Manager."""
        project_name = 'test_advanced_project'
        template = 'python'
        
        result = self.project_manager.create_project(project_name, template)
        
        self.assertTrue(result['success'])
        self.assertEqual(result['project_name'], project_name)
        
        # Check if project directory was created
        project_path = self.test_workspace / project_name
        self.assertTrue(project_path.exists())


class TestToolIntegration(unittest.TestCase):
    """Integration tests for tools working together."""
    
    def setUp(self):
        """Set up integration test environment."""
        self.test_workspace = TEST_CONFIG['workspace_dir'] / 'tool_integration'
        self.test_workspace.mkdir(exist_ok=True)
        self.file_tool = EnhancedFileTool(self.test_workspace)
    
    def tearDown(self):
        """Clean up integration test environment."""
        if self.test_workspace.exists():
            shutil.rmtree(self.test_workspace, ignore_errors=True)
    
    def test_project_workflow(self):
        """Test complete project workflow using multiple tools."""
        # Step 1: Create project
        result = self.file_tool.create_project('integration_test', 'python')
        self.assertTrue(result['success'])
        
        # Step 2: Analyze the created project
        result = self.file_tool.analyze_code_complexity('integration_test/main.py')
        self.assertTrue(result['success'])
        
        # Step 3: Generate tests
        result = self.file_tool.generate_test_template('integration_test/main.py')
        self.assertTrue(result['success'])
        
        # Step 4: Create backup
        result = self.file_tool.create_backup('integration_test/main.py')
        self.assertTrue(result['success'])
    
    def test_file_operations_workflow(self):
        """Test file operations workflow."""
        # Create initial file
        test_file = 'workflow_test.py'
        content = 'def test(): pass'
        
        # Write file
        with open(self.test_workspace / test_file, 'w') as f:
            f.write(content)
        
        # Search for pattern
        result = self.file_tool.advanced_grep('def', '*.py')
        self.assertTrue(result['success'])
        self.assertGreater(result['total_matches'], 0)
        
        # Calculate checksum
        result = self.file_tool.calculate_checksums('*.py')
        self.assertTrue(result['success'])
        
        # Create backup
        result = self.file_tool.create_backup(test_file)
        self.assertTrue(result['success'])


if __name__ == '__main__':
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_suite.addTest(unittest.makeSuite(TestEnhancedFileTool))
    test_suite.addTest(unittest.makeSuite(TestToolManager))
    test_suite.addTest(unittest.makeSuite(TestAdvancedTools))
    test_suite.addTest(unittest.makeSuite(TestToolIntegration))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print(f"\nTool Tests Summary:")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\nFailures:")
        for test, traceback in result.failures:
            print(f"- {test}")
    
    if result.errors:
        print("\nErrors:")
        for test, traceback in result.errors:
            print(f"- {test}")
