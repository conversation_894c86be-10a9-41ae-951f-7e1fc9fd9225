"""
Agent module for the Advanced AI Agent.
"""

import re
import json
from pathlib import Path
from typing import Dict, List, Optional, Any, Generator, Callable

from models import ModelManager
from conversation import Conversation, ConversationManager
from tools import (
    ShellTool, FileTool, CodeTool, WebTool, CodebaseTool, VisionTool,
    PatchTool, BrowserTool, RagTool, SearchAPI,
    WebScraperTool, InformationSynthesizer, WebInfoManager
)
from tools.tool_manager import <PERSON><PERSON>, ToolManager
from core.ai_code_assistant import AICodeAssistant, AssistantRequest
from utils import get_logger

# Get the logger
logger = get_logger()

class Agent:
    """The AI agent with enhanced capabilities."""

    def __init__(
        self,
        model_manager: ModelManager,
        conversation_manager: ConversationManager,
        workspace_dir: Optional[Path] = None,
        system_prompt: Optional[str] = None
    ):
        """Initialize the agent.

        Args:
            model_manager: The model manager to use.
            conversation_manager: The conversation manager to use.
            workspace_dir: The workspace directory to use. If None, will use the current directory.
            system_prompt: The system prompt to use. If None, will use a default prompt.
        """
        self.model_manager = model_manager
        self.conversation_manager = conversation_manager
        self.workspace_dir = workspace_dir or Path.cwd()
        self._has_rag = False

        # Initialize tool manager
        self.tool_manager = ToolManager()

        # Initialize tools first, then set the system prompt
        self._initialize_tools()

        # Initialize AI Code Assistant
        self.ai_code_assistant = AICodeAssistant(self.model_manager, self.workspace_dir)

        # Set the system prompt
        if system_prompt is None:
            self.system_prompt = self._get_default_system_prompt()
        else:
            self.system_prompt = system_prompt

    def _initialize_tools(self):
        """Initialize and register the tools with the ToolManager."""
        # Initialize individual tool instances
        self.shell_tool = ShellTool(self.workspace_dir)
        self.file_tool = FileTool(self.workspace_dir)
        self.code_tool = CodeTool()
        self.web_tool = WebTool()
        self.codebase_tool = CodebaseTool(self.workspace_dir)
        self.vision_tool = VisionTool(self.workspace_dir)
        self.patch_tool = PatchTool(self.workspace_dir)
        self.browser_tool = BrowserTool()
        self.search_api = SearchAPI()
        self.web_scraper = WebScraperTool()
        self.info_synthesizer = InformationSynthesizer()
        self.web_info_manager = WebInfoManager()

        # Initialize RAG tool if dependencies are available
        try:
            import importlib.util
            has_faiss = importlib.util.find_spec("faiss") is not None
            has_sentence_transformers = importlib.util.find_spec("sentence_transformers") is not None

            if has_faiss and has_sentence_transformers:
                self.rag_tool = RagTool(self.workspace_dir)
                self._has_rag = True
            else:
                self._has_rag = False
        except Exception:
            self._has_rag = False

        # Register tools with the ToolManager
        self._register_all_tools()

    def _register_all_tools(self):
        """Register all tools with the tool manager."""
        self._register_shell_tools()
        self._register_file_tools()
        self._register_code_tools()
        self._register_web_tools()
        self._register_codebase_tools()
        self._register_vision_tools()
        self._register_patch_tools()
        self._register_browser_tools()
        self._register_scrape_tools()
        self._register_info_tools()
        self._register_ai_assistant_tool()
        if self._has_rag:
            self._register_rag_tool()

    def _register_shell_tools(self):
        """Register shell-related tools."""
        self.tool_manager.register_tool(Tool(
            name="shell",
            description="Execute shell commands on the system.",
            function=self._execute_shell,
            parameters={
                "type": "object",
                "properties": {
                    "command": {"type": "string", "description": "The shell command to execute."}
                },
                "required": ["command"]
            }
        ))

    def _register_file_tools(self):
        """Register file-related tools."""
        self.tool_manager.register_tool(Tool(
            name="file_read",
            description="Read the content of a file.",
            function=self._file_read,
            parameters={
                "type": "object",
                "properties": {
                    "path": {"type": "string", "description": "The path to the file to read."}
                },
                "required": ["path"]
            }
        ))

        self.tool_manager.register_tool(Tool(
            name="file_write",
            description="Write content to a file. Overwrites if the file exists.",
            function=self._file_write,
            parameters={
                "type": "object",
                "properties": {
                    "path": {"type": "string", "description": "The path to the file to write."},
                    "content": {"type": "string", "description": "The content to write to the file."}
                },
                "required": ["path", "content"]
            }
        ))

        self.tool_manager.register_tool(Tool(
            name="file_append",
            description="Append content to an existing file.",
            function=self._file_append,
            parameters={
                "type": "object",
                "properties": {
                    "path": {"type": "string", "description": "The path to the file to append to."},
                    "content": {"type": "string", "description": "The content to append to the file."}
                },
                "required": ["path", "content"]
            }
        ))

        self.tool_manager.register_tool(Tool(
            name="file_delete",
            description="Delete a file.",
            function=self._file_delete,
            parameters={
                "type": "object",
                "properties": {
                    "path": {"type": "string", "description": "The path to the file to delete."}
                },
                "required": ["path"]
            }
        ))

        self.tool_manager.register_tool(Tool(
            name="file_list",
            description="List files and directories in a given path.",
            function=self._file_list,
            parameters={
                "type": "object",
                "properties": {
                    "path": {"type": "string", "description": "The directory path to list. Defaults to current directory."}
                },
                "required": []
            }
        ))

    def _register_code_tools(self):
        """Register code execution tools."""
        self.tool_manager.register_tool(Tool(
            name="code_execute",
            description="Execute code in a specified programming language.",
            function=self._execute_code,
            parameters={
                "type": "object",
                "properties": {
                    "language": {"type": "string", "description": "The programming language (e.g., 'python', 'javascript')."},
                    "code": {"type": "string", "description": "The code to execute."}
                },
                "required": ["language", "code"]
            }
        ))

    def _register_web_tools(self):
        """Register web-related tools."""
        self.tool_manager.register_tool(Tool(
            name="web_search",
            description="Search the web for information.",
            function=self._execute_web_search,
            parameters={
                "type": "object",
                "properties": {
                    "query": {"type": "string", "description": "The search query."}
                },
                "required": ["query"]
            }
        ))

        self.tool_manager.register_tool(Tool(
            name="web_fetch",
            description="Fetch content from a given URL.",
            function=self._execute_web_fetch,
            parameters={
                "type": "object",
                "properties": {
                    "url": {"type": "string", "description": "The URL to fetch."}
                },
                "required": ["url"]
            }
        ))

    def _register_codebase_tools(self):
        """Register codebase analysis tools."""
        self.tool_manager.register_tool(Tool(
            name="codebase_find_files",
            description="Find files in the codebase matching a pattern.",
            function=self._execute_codebase_find_files,
            parameters={
                "type": "object",
                "properties": {
                    "pattern": {"type": "string", "description": "The pattern to search for (e.g., '*.py', 'config.json'). Defaults to '*'."}
                },
                "required": []
            }
        ))

        self.tool_manager.register_tool(Tool(
            name="codebase_search",
            description="Search for a pattern within code files.",
            function=self._execute_codebase_search,
            parameters={
                "type": "object",
                "properties": {
                    "pattern": {"type": "string", "description": "The regex pattern to search for."},
                    "file_pattern": {"type": "string", "description": "Optional glob pattern to filter files (e.g., '*.js'). Defaults to '*'."}
                },
                "required": ["pattern"]
            }
        ))

    def _register_vision_tools(self):
        """Register vision-related tools."""
        self.tool_manager.register_tool(Tool(
            name="vision_take_screenshot",
            description="Take a screenshot of the current screen.",
            function=self._execute_vision_take_screenshot,
            parameters={
                "type": "object",
                "properties": {
                    "path": {"type": "string", "description": "Optional path to save the screenshot. Defaults to a temporary file."}
                },
                "required": []
            }
        ))

    def _register_patch_tools(self):
        """Register patch-related tools."""
        self.tool_manager.register_tool(Tool(
            name="patch_apply",
            description="Apply a patch to a file.",
            function=self._execute_patch_apply,
            parameters={
                "type": "object",
                "properties": {
                    "file_path": {"type": "string", "description": "The path to the file to patch."},
                    "original_code": {"type": "string", "description": "The original code snippet to replace."},
                    "updated_code": {"type": "string", "description": "The new code snippet to insert."}
                },
                "required": ["file_path", "original_code", "updated_code"]
            }
        ))

    def _register_browser_tools(self):
        """Register browser-related tools."""
        self.tool_manager.register_tool(Tool(
            name="browser_read_url",
            description="Browse a URL and get its content.",
            function=self._execute_browser_read_url,
            parameters={
                "type": "object",
                "properties": {
                    "url": {"type": "string", "description": "The URL to browse."}
                },
                "required": ["url"]
            }
        ))

    def _register_scrape_tools(self):
        """Register web scraping tools."""
        self.tool_manager.register_tool(Tool(
            name="scrape_website",
            description="Scrape content from a website.",
            function=self._execute_scrape_website,
            parameters={
                "type": "object",
                "properties": {
                    "url": {"type": "string", "description": "The URL to scrape."},
                    "selector": {"type": "string", "description": "Optional CSS selector to target specific content."}
                },
                "required": ["url"]
            }
        ))

    def _register_info_tools(self):
        """Register information synthesis tools."""
        self.tool_manager.register_tool(Tool(
            name="synthesize_info",
            description="Synthesize information from multiple sources.",
            function=self._execute_synthesize_info,
            parameters={
                "type": "object",
                "properties": {
                    "sources": {"type": "array", "items": {"type": "string"}, "description": "List of information sources."},
                    "query": {"type": "string", "description": "The query to synthesize information for."}
                },
                "required": ["sources", "query"]
            }
        ))

    def _register_ai_assistant_tool(self):
        """Register AI code assistant tool."""
        self.tool_manager.register_tool(Tool(
            name="ai_code_assist",
            description="Get AI assistance for code-related tasks.",
            function=self._execute_ai_code_assist,
            parameters={
                "type": "object",
                "properties": {
                    "task": {"type": "string", "description": "The coding task or question."},
                    "context": {"type": "string", "description": "Optional context or code snippet."}
                },
                "required": ["task"]
            }
        ))

    def _register_rag_tool(self):
        """Register RAG (Retrieval Augmented Generation) tool."""
        if self._has_rag:
            self.tool_manager.register_tool(Tool(
                name="rag_query",
                description="Query the RAG system for relevant information.",
                function=self._execute_rag_query,
                parameters={
                    "type": "object",
                    "properties": {
                        "query": {"type": "string", "description": "The query to search for."},
                        "top_k": {"type": "integer", "description": "Number of top results to return. Defaults to 5."}
                    },
                    "required": ["query"]
                }
            ))

    # Tool execution methods
    def _execute_shell(self, command: str) -> str:
        """Execute a shell command."""
        if not command.strip():
            return "Error: No command specified."

        try:
            stdout, stderr, return_code = self.shell_tool.execute(command)

            if return_code == 0:
                if stdout:
                    return f"Command executed successfully:\n\n{stdout}"
                return "Command executed successfully (no output)"
            else:
                return f"Command failed (exit code {return_code}):\n\n{stderr}"
        except Exception as e:
            return f"Error executing command: {e}"

    def _file_read(self, path: str) -> str:
        """Read the content of a file."""
        try:
            return self.file_tool.read(path)
        except Exception as e:
            return f"Error reading file: {e}"

    def _file_write(self, path: str, content: str) -> str:
        """Write content to a file."""
        try:
            self.file_tool.write(path, content)
            return f"File written successfully: {path}"
        except Exception as e:
            return f"Error writing file: {e}"

    def _file_append(self, path: str, content: str) -> str:
        """Append content to a file."""
        try:
            self.file_tool.append(path, content)
            return f"Content appended successfully to: {path}"
        except Exception as e:
            return f"Error appending to file: {e}"

    def _file_delete(self, path: str) -> str:
        """Delete a file."""
        try:
            self.file_tool.delete(path)
            return f"File deleted successfully: {path}"
        except Exception as e:
            return f"Error deleting file: {e}"

    def _file_list(self, path: str = ".") -> str:
        """List files and directories."""
        try:
            files = self.file_tool.list(path)
            return json.dumps(files, indent=2)
        except Exception as e:
            return f"Error listing files: {e}"

    def _execute_code(self, language: str, code: str) -> str:
        """Execute code in a programming language."""
        try:
            stdout, stderr, return_code = self.code_tool.execute(code, language)

            if return_code == 0:
                if stdout:
                    return f"Code executed successfully:\n\n{stdout}"
                return "Code executed successfully (no output)"
            else:
                return f"Code execution failed (exit code {return_code}):\n\n{stderr}"
        except Exception as e:
            return f"Error executing code: {e}"

    def _execute_web_search(self, query: str) -> str:
        """Execute a web search."""
        try:
            results = self.web_tool.search(query)
            return json.dumps(results, indent=2)
        except Exception as e:
            return f"Error performing web search: {e}"

    def _execute_web_fetch(self, url: str) -> str:
        """Fetch content from a URL."""
        try:
            content, metadata = self.web_tool.fetch_url(url)
            return (
                f"URL: {url}\n"
                f"Title: {metadata.get('title', 'N/A')}\n"
                f"Content length: {len(content)} characters\n\n"
                f"{content[:2000]}..."
            )
        except Exception as e:
            return f"Error fetching URL: {e}"

    def _execute_codebase_find_files(self, pattern: str = "*") -> str:
        """Find files in the codebase."""
        try:
            files = self.codebase_tool.find_files(pattern)
            if files:
                return f"Found {len(files)} files matching '{pattern}':\n\n{json.dumps(files, indent=2)}"
            else:
                return f"No files found matching pattern: {pattern}"
        except Exception as e:
            return f"Error finding files: {e}"

    def _execute_codebase_search(self, pattern: str, file_pattern: str = "*") -> str:
        """Search for a pattern within code files."""
        try:
            results = self.codebase_tool.search(pattern, file_pattern)
            if results:
                return f"Search results for '{pattern}' in '{file_pattern}':\n\n{json.dumps(results, indent=2)}"
            else:
                return f"No matches found for pattern '{pattern}' in files matching '{file_pattern}'"
        except Exception as e:
            return f"Error searching codebase: {e}"

    def _execute_vision_take_screenshot(self, path: str = None) -> str:
        """Take a screenshot."""
        try:
            screenshot_path = self.vision_tool.take_screenshot(path)
            return f"Screenshot saved to: {screenshot_path}"
        except Exception as e:
            return f"Error taking screenshot: {e}"

    def _execute_patch_apply(self, file_path: str, original_code: str, updated_code: str) -> str:
        """Apply a patch to a file."""
        try:
            success = self.patch_tool.apply(file_path, original_code, updated_code)
            if success:
                return f"Patch successfully applied to {file_path}"
            else:
                return f"Failed to apply patch to {file_path}"
        except Exception as e:
            return f"Error applying patch: {e}"

    def _execute_browser_read_url(self, url: str) -> str:
        """Browse a URL and get its content."""
        try:
            content, metadata = self.browser_tool.read_url(url)
            return (
                f"Content from {url}:\n"
                f"Title: {metadata.get('title', 'N/A')}\n"
                f"Content length: {len(content)} characters\n\n"
                f"{content[:2000]}..."
            )
        except Exception as e:
            return f"Error browsing URL: {e}"

    def _execute_scrape_website(self, url: str, selector: str = None) -> str:
        """Scrape content from a website."""
        try:
            content = self.web_scraper.scrape(url, selector)
            return f"Scraped content from {url}:\n\n{content[:2000]}..."
        except Exception as e:
            return f"Error scraping website: {e}"

    def _execute_synthesize_info(self, sources: List[str], query: str) -> str:
        """Synthesize information from multiple sources."""
        try:
            result = self.info_synthesizer.synthesize(sources, query)
            return f"Synthesized information for '{query}':\n\n{result}"
        except Exception as e:
            return f"Error synthesizing information: {e}"

    def _execute_ai_code_assist(self, task: str, context: str = None) -> str:
        """Get AI assistance for code-related tasks."""
        try:
            request = AssistantRequest(task=task, context=context or "")
            response = self.ai_code_assistant.process_request(request)
            return response.content
        except Exception as e:
            return f"Error getting AI assistance: {e}"

    def _execute_rag_query(self, query: str, top_k: int = 5) -> str:
        """Query the RAG system."""
        if not self._has_rag:
            return "Error: RAG system not available. Please install faiss-cpu and sentence-transformers."

        try:
            results = self.rag_tool.query(query, top_k)
            return f"RAG query results for '{query}':\n\n{json.dumps(results, indent=2)}"
        except Exception as e:
            return f"Error querying RAG system: {e}"

    def _get_default_system_prompt(self) -> str:
        """Get the default system prompt for the agent."""
        tool_descriptions = []
        for tool_name, tool in self.tool_manager.tools.items():
            tool_descriptions.append(f"- {tool_name}: {tool.description}")

        tool_list = "\n".join(tool_descriptions)

        return f"""You are an advanced AI coding assistant with access to powerful tools.

Available tools:
{tool_list}

You can use these tools by including them in your response using the following format:
```tool_name
arguments
```

For example:
```shell
ls -la
```

Or:
```file_write
path: example.py
content: print("Hello, World!")
```

Always provide clear explanations of what you're doing and why.
If you're not sure about something, ask for clarification.
Be helpful, accurate, and concise.
"""

    def process_message(self, message: str, conversation = None) -> str:
        """Process a message from the user.

        Args:
            message: The message from the user.
            conversation: The conversation to add the message to.

        Returns:
            The response from the agent.
        """
        # Get the conversation
        if conversation is None:
            if self.conversation_manager.current_conversation is None:
                self.conversation_manager.new_conversation()
            conversation = self.conversation_manager.current_conversation

        # Add the user message to the conversation
        conversation.add_message("user", message)

        # Generate the response
        response = self.model_manager.generate(
            prompt=message,
            system_prompt=self.system_prompt
        )

        # Process the response for tool calls
        processed_response = self._process_response(response, conversation)

        # Add the assistant message to the conversation
        conversation.add_message("assistant", processed_response)

        # Save the conversation
        self.conversation_manager.save_conversation()

        return processed_response

    def stream_process_message(self, message: str, conversation = None):
        """Process a message from the user and stream the response.

        Args:
            message: The message from the user.
            conversation: The conversation to add the message to.

        Yields:
            Chunks of the response.
        """
        # Get the conversation
        if conversation is None:
            if self.conversation_manager.current_conversation is None:
                self.conversation_manager.new_conversation()
            conversation = self.conversation_manager.current_conversation

        # Add the user message to the conversation
        conversation.add_message("user", message)

        # Generate the response using streaming
        response_chunks = []
        for chunk in self.model_manager.stream_generate(
            prompt=message,
            system_prompt=self.system_prompt
        ):
            response_chunks.append(chunk)
            yield chunk

        # Process the response for tool calls
        response = "".join(response_chunks)
        processed_response = self._process_response(response, conversation)

        # If the response was processed (tool calls), yield the processed response
        if processed_response != response:
            yield processed_response

        # Add the assistant message to the conversation
        conversation.add_message("assistant", processed_response)

        # Save the conversation
        self.conversation_manager.save_conversation()

    def _process_response(self, response: str, conversation) -> str:
        """Process a response for tool calls.

        Args:
            response: The response to process.
            conversation: The conversation to add tool messages to.

        Returns:
            The processed response.
        """
        import re

        # Check for tool calls
        tool_pattern = r"```(\w+)\n(.*?)```"
        tool_matches = re.finditer(tool_pattern, response, re.DOTALL)

        processed_response = response

        for match in tool_matches:
            tool_name = match.group(1)
            tool_args = match.group(2).strip()

            # Check if the tool exists
            if tool_name in self.tool_manager.tools:
                try:
                    # Execute the tool
                    tool_result = self.tool_manager.execute_tool(tool_name, tool_args)

                    # Add the tool call and result to the conversation
                    conversation.add_message("tool", f"```{tool_name}\n{tool_args}\n```")
                    conversation.add_message("tool_result", tool_result)

                    # Replace the tool call with the result in the response
                    processed_response = processed_response.replace(
                        match.group(0),
                        f"```{tool_name}\n{tool_args}\n```\n\n**Result:**\n{tool_result}"
                    )
                except Exception as e:
                    error_msg = f"Error executing tool {tool_name}: {e}"
                    processed_response = processed_response.replace(
                        match.group(0),
                        f"```{tool_name}\n{tool_args}\n```\n\n**Error:**\n{error_msg}"
                    )

        return processed_response
