"""
Enhanced File Tool with Advanced Capabilities
Integrates all advanced file operations, project management, and development tools.
"""

import os
import json
import time
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple

from .file import FileTool
from .advanced_tools import (
    AdvancedFileManager, AdvancedTerminalManager, 
    AdvancedProjectManager, AdvancedCodeAnalyzer,
    AdvancedTestManager, AdvancedDeploymentManager
)
from utils import get_logger

logger = get_logger()

class EnhancedFileTool(FileTool):
    """Enhanced file tool with advanced capabilities."""
    
    def __init__(self, workspace_dir: Path):
        super().__init__(workspace_dir)
        self.advanced_file_manager = AdvancedFileManager(workspace_dir)
        self.terminal_manager = AdvancedTerminalManager(workspace_dir)
        self.project_manager = AdvancedProjectManager(workspace_dir)
        self.code_analyzer = AdvancedCodeAnalyzer(workspace_dir)
        self.test_manager = AdvancedTestManager(workspace_dir)
        self.deployment_manager = AdvancedDeploymentManager(workspace_dir)
    
    def advanced_grep(self, pattern: str, file_pattern: str = "*", 
                     context_lines: int = 3, case_sensitive: bool = False) -> Dict[str, Any]:
        """Advanced grep with context and options."""
        try:
            results = self.advanced_file_manager.advanced_grep(
                pattern, file_pattern, context_lines, case_sensitive
            )
            
            formatted_results = []
            for result in results:
                formatted_results.append({
                    'file': result.file_path,
                    'line': result.line_number,
                    'content': result.content,
                    'context_before': result.context_before,
                    'context_after': result.context_after,
                    'match_start': result.match_start,
                    'match_end': result.match_end
                })
            
            return {
                'success': True,
                'pattern': pattern,
                'file_pattern': file_pattern,
                'total_matches': len(formatted_results),
                'results': formatted_results
            }
            
        except Exception as e:
            logger.error(f"Error in advanced grep: {e}")
            return {
                'success': False,
                'error': str(e),
                'pattern': pattern,
                'file_pattern': file_pattern
            }
    
    def create_backup(self, file_path: str, backup_dir: Optional[str] = None) -> Dict[str, Any]:
        """Create backup of a file."""
        try:
            backup_path = self.advanced_file_manager.create_backup(file_path, backup_dir)
            return {
                'success': True,
                'original_file': file_path,
                'backup_path': backup_path,
                'timestamp': time.time()
            }
        except Exception as e:
            logger.error(f"Error creating backup: {e}")
            return {
                'success': False,
                'error': str(e),
                'original_file': file_path
            }
    
    def batch_rename(self, pattern: str, replacement: str, file_pattern: str = "*") -> Dict[str, Any]:
        """Batch rename files using regex pattern."""
        try:
            renamed_files = self.advanced_file_manager.batch_rename(pattern, replacement, file_pattern)
            return {
                'success': True,
                'pattern': pattern,
                'replacement': replacement,
                'renamed_count': len(renamed_files),
                'renamed_files': renamed_files
            }
        except Exception as e:
            logger.error(f"Error in batch rename: {e}")
            return {
                'success': False,
                'error': str(e),
                'pattern': pattern,
                'replacement': replacement
            }
    
    def calculate_checksums(self, file_pattern: str = "*") -> Dict[str, Any]:
        """Calculate MD5 checksums for files."""
        try:
            checksums = self.advanced_file_manager.calculate_checksums(file_pattern)
            return {
                'success': True,
                'file_pattern': file_pattern,
                'file_count': len(checksums),
                'checksums': checksums
            }
        except Exception as e:
            logger.error(f"Error calculating checksums: {e}")
            return {
                'success': False,
                'error': str(e),
                'file_pattern': file_pattern
            }
    
    def create_terminal_session(self, session_id: str, shell: str = "bash") -> Dict[str, Any]:
        """Create a persistent terminal session."""
        try:
            success = self.terminal_manager.create_session(session_id, shell)
            return {
                'success': success,
                'session_id': session_id,
                'shell': shell,
                'message': f"Session {session_id} created" if success else f"Failed to create session {session_id}"
            }
        except Exception as e:
            logger.error(f"Error creating terminal session: {e}")
            return {
                'success': False,
                'error': str(e),
                'session_id': session_id
            }
    
    def execute_in_session(self, session_id: str, command: str, timeout: float = 30.0) -> Dict[str, Any]:
        """Execute command in a specific terminal session."""
        try:
            stdout, stderr, return_code = self.terminal_manager.execute_in_session(
                session_id, command, timeout
            )
            return {
                'success': return_code == 0,
                'session_id': session_id,
                'command': command,
                'stdout': stdout,
                'stderr': stderr,
                'return_code': return_code
            }
        except Exception as e:
            logger.error(f"Error executing in session: {e}")
            return {
                'success': False,
                'error': str(e),
                'session_id': session_id,
                'command': command
            }
    
    def get_session_history(self, session_id: str) -> Dict[str, Any]:
        """Get command history for a terminal session."""
        try:
            history = self.terminal_manager.get_session_history(session_id)
            return {
                'success': True,
                'session_id': session_id,
                'command_count': len(history),
                'history': history
            }
        except Exception as e:
            logger.error(f"Error getting session history: {e}")
            return {
                'success': False,
                'error': str(e),
                'session_id': session_id
            }
    
    def close_terminal_session(self, session_id: str) -> Dict[str, Any]:
        """Close a terminal session."""
        try:
            success = self.terminal_manager.close_session(session_id)
            return {
                'success': success,
                'session_id': session_id,
                'message': f"Session {session_id} closed" if success else f"Failed to close session {session_id}"
            }
        except Exception as e:
            logger.error(f"Error closing terminal session: {e}")
            return {
                'success': False,
                'error': str(e),
                'session_id': session_id
            }
    
    def create_project(self, project_name: str, template: str, options: Dict[str, Any] = None) -> Dict[str, Any]:
        """Create a new project from template."""
        try:
            result = self.project_manager.create_project(project_name, template, options)
            return result
        except Exception as e:
            logger.error(f"Error creating project: {e}")
            return {
                'success': False,
                'error': str(e),
                'project_name': project_name,
                'template': template
            }
    
    def analyze_code_complexity(self, file_path: str) -> Dict[str, Any]:
        """Analyze code complexity metrics."""
        try:
            analysis = self.code_analyzer.analyze_complexity(file_path)
            return {
                'success': True,
                'file_path': file_path,
                'analysis': analysis
            }
        except Exception as e:
            logger.error(f"Error analyzing code complexity: {e}")
            return {
                'success': False,
                'error': str(e),
                'file_path': file_path
            }
    
    def find_code_smells(self, file_path: str) -> Dict[str, Any]:
        """Find potential code smells."""
        try:
            smells = self.code_analyzer.find_code_smells(file_path)
            return {
                'success': True,
                'file_path': file_path,
                'smell_count': len(smells),
                'smells': smells
            }
        except Exception as e:
            logger.error(f"Error finding code smells: {e}")
            return {
                'success': False,
                'error': str(e),
                'file_path': file_path
            }
    
    def generate_test_template(self, file_path: str, test_framework: str = 'unittest') -> Dict[str, Any]:
        """Generate test template for a given file."""
        try:
            template = self.test_manager.generate_test_template(file_path, test_framework)
            test_file_path = f"test_{Path(file_path).stem}.py"
            
            return {
                'success': True,
                'source_file': file_path,
                'test_file': test_file_path,
                'framework': test_framework,
                'template': template
            }
        except Exception as e:
            logger.error(f"Error generating test template: {e}")
            return {
                'success': False,
                'error': str(e),
                'source_file': file_path,
                'framework': test_framework
            }
    
    def run_tests(self, test_pattern: str = "test_*.py", framework: str = "unittest") -> Dict[str, Any]:
        """Run tests and return results."""
        try:
            result = self.test_manager.run_tests(test_pattern, framework)
            return result
        except Exception as e:
            logger.error(f"Error running tests: {e}")
            return {
                'success': False,
                'error': str(e),
                'test_pattern': test_pattern,
                'framework': framework
            }
    
    def generate_dockerfile(self, project_type: str, options: Dict[str, Any] = None) -> Dict[str, Any]:
        """Generate Dockerfile for project."""
        try:
            dockerfile_content = self.deployment_manager.generate_dockerfile(project_type, options)
            return {
                'success': True,
                'project_type': project_type,
                'dockerfile': dockerfile_content,
                'options': options or {}
            }
        except Exception as e:
            logger.error(f"Error generating Dockerfile: {e}")
            return {
                'success': False,
                'error': str(e),
                'project_type': project_type
            }
    
    def generate_docker_compose(self, services: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate docker-compose.yml file."""
        try:
            compose_content = self.deployment_manager.generate_docker_compose(services)
            return {
                'success': True,
                'service_count': len(services),
                'docker_compose': compose_content,
                'services': services
            }
        except Exception as e:
            logger.error(f"Error generating docker-compose: {e}")
            return {
                'success': False,
                'error': str(e),
                'services': services
            }
    
    def generate_github_actions(self, project_type: str, options: Dict[str, Any] = None) -> Dict[str, Any]:
        """Generate GitHub Actions workflow."""
        try:
            workflow_content = self.deployment_manager.generate_github_actions(project_type, options)
            return {
                'success': True,
                'project_type': project_type,
                'workflow': workflow_content,
                'options': options or {}
            }
        except Exception as e:
            logger.error(f"Error generating GitHub Actions: {e}")
            return {
                'success': False,
                'error': str(e),
                'project_type': project_type
            }
