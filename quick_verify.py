#!/usr/bin/env python3
"""
Quick verification script to check if the Enhanced AI Agent is working.
"""

import sys
from pathlib import Path

def check_imports():
    """Check if all required modules can be imported."""
    print("🔍 Checking imports...")
    
    try:
        # Check core modules
        print("  ✓ Checking core modules...")
        from agent import <PERSON>hanced<PERSON><PERSON>, WorkflowStatus, TaskType
        print("    ✅ agent module imported successfully")
        
        # Check if we can create a mock agent
        from unittest.mock import Mock
        
        # Mock the dependencies that might not be available
        mock_model_manager = Mock()
        mock_conversation_manager = Mock()
        
        # Try to create agent
        workspace_dir = Path.cwd() / 'test_workspace'
        workspace_dir.mkdir(exist_ok=True)
        
        agent = EnhancedAgent(
            model_manager=mock_model_manager,
            conversation_manager=mock_conversation_manager,
            workspace_dir=workspace_dir
        )
        
        print("    ✅ EnhancedAgent created successfully")
        
        # Check tool registration
        tools = agent.tool_manager.tools
        print(f"    ✅ {len(tools)} tools registered")
        
        # Check essential tools
        essential_tools = [
            'shell_execute', 'file_read', 'file_write', 'file_delete',
            'create_project', 'advanced_grep'
        ]
        
        missing_tools = [tool for tool in essential_tools if tool not in tools]
        if missing_tools:
            print(f"    ⚠️  Missing tools: {missing_tools}")
        else:
            print("    ✅ All essential tools registered")
        
        # Test a simple operation
        test_result = agent._file_write('test.txt', 'Hello, World!')
        if 'successfully' in test_result.lower():
            print("    ✅ File operations working")
        else:
            print(f"    ⚠️  File operation issue: {test_result}")
        
        # Clean up
        test_file = workspace_dir / 'test.txt'
        if test_file.exists():
            test_file.unlink()
        
        return True
        
    except ImportError as e:
        print(f"    ❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"    ❌ Error: {e}")
        return False

def check_tools():
    """Check if tools are working."""
    print("\n🛠️  Checking tools...")
    
    try:
        from tools.tool_manager import ToolManager, Tool
        print("    ✅ ToolManager imported")
        
        from tools.enhanced_file_tool import EnhancedFileTool
        print("    ✅ EnhancedFileTool imported")
        
        from tools.advanced_tools import AdvancedFileManager
        print("    ✅ AdvancedTools imported")
        
        return True
        
    except ImportError as e:
        print(f"    ❌ Tool import error: {e}")
        return False
    except Exception as e:
        print(f"    ❌ Tool error: {e}")
        return False

def check_core_components():
    """Check core components."""
    print("\n🧠 Checking core components...")
    
    try:
        from core.intelligent_workflow_manager import IntelligentWorkflowManager
        print("    ✅ IntelligentWorkflowManager imported")
        
        from core.fullstack_project_manager import FullStackProjectManager
        print("    ✅ FullStackProjectManager imported")
        
        return True
        
    except ImportError as e:
        print(f"    ❌ Core component import error: {e}")
        return False
    except Exception as e:
        print(f"    ❌ Core component error: {e}")
        return False

def check_file_structure():
    """Check if all required files exist."""
    print("\n📁 Checking file structure...")
    
    required_files = [
        'agent.py',
        'tools/__init__.py',
        'tools/tool_manager.py',
        'tools/enhanced_file_tool.py',
        'core/__init__.py',
        'core/intelligent_workflow_manager.py',
        'core/fullstack_project_manager.py'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
        else:
            print(f"    ✅ {file_path}")
    
    if missing_files:
        print(f"    ❌ Missing files: {missing_files}")
        return False
    
    return True

def main():
    """Main verification function."""
    print("🚀 Enhanced AI Agent - Quick Verification")
    print("=" * 50)
    
    checks = [
        ("File Structure", check_file_structure),
        ("Core Components", check_core_components),
        ("Tools", check_tools),
        ("Main Agent", check_imports)
    ]
    
    passed = 0
    total = len(checks)
    
    for check_name, check_func in checks:
        print(f"\n🔧 {check_name}")
        print("-" * 30)
        
        try:
            if check_func():
                print(f"✅ {check_name}: PASSED")
                passed += 1
            else:
                print(f"❌ {check_name}: FAILED")
        except Exception as e:
            print(f"💥 {check_name}: ERROR - {e}")
    
    # Summary
    success_rate = (passed / total) * 100
    print(f"\n📊 VERIFICATION SUMMARY")
    print("=" * 30)
    print(f"✅ Passed: {passed}/{total} ({success_rate:.1f}%)")
    print(f"❌ Failed: {total - passed}/{total}")
    
    if success_rate >= 75:
        print("\n🎉 Agent is ready to use!")
        print("\nNext steps:")
        print("1. Run: python tests/run_all_tests.py")
        print("2. Try: python verify_integration.py")
        print("3. Start coding with the Enhanced AI Agent!")
    else:
        print("\n⚠️  Agent needs attention before use")
        print("\nTroubleshooting:")
        print("1. Check that all dependencies are installed")
        print("2. Verify Python version (3.8+ required)")
        print("3. Check file permissions")
    
    return 0 if success_rate >= 75 else 1

if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
