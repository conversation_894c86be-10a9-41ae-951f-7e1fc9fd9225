import ast
import re
import time
import logging
import threading
import hashlib
from typing import Dict, List, Optional, Any, Tuple, Set, Union
from dataclasses import dataclass, asdict
from pathlib import Path
from enum import Enum
import json
import uuid

logger = logging.getLogger(__name__)

class LanguageType(Enum):
    PYTHON = "python"
    JAVASCRIPT = "javascript"
    TYPESCRIPT = "typescript"
    JAVA = "java"
    CPP = "cpp"
    GO = "go"
    RUST = "rust"

class CodeElementType(Enum):
    FUNCTION = "function"
    CLASS = "class"
    METHOD = "method"
    VARIABLE = "variable"
    CONSTANT = "constant"
    IMPORT = "import"
    MODULE = "module"
    INTERFACE = "interface"
    ENUM = "enum"
    STRUCT = "struct"

@dataclass
class CodeElement:
    element_id: str
    name: str
    element_type: CodeElementType
    language: LanguageType
    file_path: str
    line_start: int
    line_end: int
    column_start: int
    column_end: int
    signature: str
    docstring: Optional[str]
    parameters: List[str]
    return_type: Optional[str]
    dependencies: List[str]
    complexity_score: float
    semantic_tags: List[str]
    code_snippet: str
    hash_signature: str

@dataclass
class CodeRelationship:
    relationship_id: str
    source_element: str
    target_element: str
    relationship_type: str  # calls, inherits, imports, uses, etc.
    strength: float  # 0.0 to 1.0
    context: Dict[str, Any]

@dataclass
class SearchResult:
    element: CodeElement
    relevance_score: float
    match_reasons: List[str]
    context_snippet: str
    related_elements: List[CodeElement]

class AdvancedCodeUnderstanding:
    def __init__(self, workspace_dir: Path, model_manager=None):
        self.workspace_dir = workspace_dir
        self.model_manager = model_manager
        self.lock = threading.RLock()

        # Code index storage
        self.code_elements: Dict[str, CodeElement] = {}
        self.relationships: Dict[str, CodeRelationship] = {}
        self.file_index: Dict[str, List[str]] = {}  # file_path -> element_ids
        self.semantic_index: Dict[str, Set[str]] = {}  # semantic_tag -> element_ids

        # Language processors
        self.language_processors = {
            LanguageType.PYTHON: self._process_python_code,
            LanguageType.JAVASCRIPT: self._process_javascript_code,
            LanguageType.TYPESCRIPT: self._process_typescript_code,
            LanguageType.JAVA: self._process_java_code,
            LanguageType.CPP: self._process_cpp_code,
            LanguageType.GO: self._process_go_code,
            LanguageType.RUST: self._process_rust_code,
        }

        # File extension mappings
        self.extension_to_language = {
            '.py': LanguageType.PYTHON,
            '.js': LanguageType.JAVASCRIPT,
            '.jsx': LanguageType.JAVASCRIPT,
            '.ts': LanguageType.TYPESCRIPT,
            '.tsx': LanguageType.TYPESCRIPT,
            '.java': LanguageType.JAVA,
            '.cpp': LanguageType.CPP,
            '.cc': LanguageType.CPP,
            '.cxx': LanguageType.CPP,
            '.c': LanguageType.CPP,
            '.h': LanguageType.CPP,
            '.hpp': LanguageType.CPP,
            '.go': LanguageType.GO,
            '.rs': LanguageType.RUST,
        }

        # Cross-language conversion mappings
        self.conversion_mappings = self._initialize_conversion_mappings()

        # Performance metrics
        self.indexing_stats = {
            'files_processed': 0,
            'elements_indexed': 0,
            'relationships_found': 0,
            'last_index_time': 0.0
        }

    def index_codebase(self, force_reindex: bool = False) -> Dict[str, Any]:
        with self.lock:
            start_time = time.time()
            logger.info("Starting codebase indexing...")

            if force_reindex:
                self._clear_index()

            # Find all code files
            code_files = self._find_code_files()

            # Process each file
            for file_path in code_files:
                try:
                    self._index_file(file_path)
                except Exception as e:
                    logger.error(f"Error indexing {file_path}: {e}")

            # Build relationships
            self._build_relationships()

            # Update statistics
            indexing_time = time.time() - start_time
            self.indexing_stats.update({
                'files_processed': len(code_files),
                'elements_indexed': len(self.code_elements),
                'relationships_found': len(self.relationships),
                'last_index_time': indexing_time
            })

            logger.info(f"Indexing completed in {indexing_time:.2f}s - "
                       f"{len(self.code_elements)} elements, "
                       f"{len(self.relationships)} relationships")

            return self.indexing_stats.copy()

    def semantic_search(self,
                       query: str,
                       language: Optional[LanguageType] = None,
                       element_type: Optional[CodeElementType] = None,
                       max_results: int = 10) -> List[SearchResult]:
        with self.lock:
            logger.info(f"Performing semantic search: '{query}'")

            # Tokenize and analyze query
            query_tokens = self._tokenize_query(query)
            semantic_intent = self._analyze_search_intent(query)

            results = []

            # Search through code elements
            for element_id, element in self.code_elements.items():
                # Apply filters
                if language and element.language != language:
                    continue
                if element_type and element.element_type != element_type:
                    continue

                # Calculate relevance score
                relevance_score, match_reasons = self._calculate_relevance(
                    element, query_tokens, semantic_intent
                )

                if relevance_score > 0.1:  # Minimum relevance threshold
                    # Get context snippet
                    context_snippet = self._get_context_snippet(element)

                    # Find related elements
                    related_elements = self._find_related_elements(element_id, max_related=3)

                    results.append(SearchResult(
                        element=element,
                        relevance_score=relevance_score,
                        match_reasons=match_reasons,
                        context_snippet=context_snippet,
                        related_elements=related_elements
                    ))

            # Sort by relevance and return top results
            results.sort(key=lambda x: x.relevance_score, reverse=True)
            return results[:max_results]

    def analyze_dependencies(self, file_path: str) -> Dict[str, Any]:
        with self.lock:
            if file_path not in self.file_index:
                return {"error": "File not indexed"}

            element_ids = self.file_index[file_path]
            dependencies = {
                'internal_dependencies': [],
                'external_dependencies': [],
                'dependency_graph': {},
                'circular_dependencies': [],
                'unused_imports': []
            }

            for element_id in element_ids:
                element = self.code_elements[element_id]

                # Analyze element dependencies
                for dep in element.dependencies:
                    if self._is_internal_dependency(dep):
                        dependencies['internal_dependencies'].append(dep)
                    else:
                        dependencies['external_dependencies'].append(dep)

                # Build dependency graph
                dependencies['dependency_graph'][element.name] = element.dependencies

            # Detect circular dependencies
            dependencies['circular_dependencies'] = self._detect_circular_dependencies(
                dependencies['dependency_graph']
            )

            return dependencies

    def convert_code_cross_language(self,
                                  code: str,
                                  source_language: LanguageType,
                                  target_language: LanguageType) -> Dict[str, Any]:
        logger.info(f"Converting code from {source_language.value} to {target_language.value}")

        try:
            # Parse source code
            source_elements = self._parse_code_structure(code, source_language)

            # Convert each element
            converted_elements = []
            conversion_notes = []

            for element in source_elements:
                converted_element, notes = self._convert_element(
                    element, source_language, target_language
                )
                converted_elements.append(converted_element)
                conversion_notes.extend(notes)

            # Generate target code
            target_code = self._generate_target_code(converted_elements, target_language)

            return {
                'success': True,
                'converted_code': target_code,
                'conversion_notes': conversion_notes,
                'source_language': source_language.value,
                'target_language': target_language.value,
                'elements_converted': len(converted_elements)
            }

        except Exception as e:
            logger.error(f"Code conversion failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'source_language': source_language.value,
                'target_language': target_language.value
            }

    def get_code_insights(self, file_path: str) -> Dict[str, Any]:
        """Get comprehensive insights about a code file.

        Args:
            file_path: Path to the file to analyze

        Returns:
            Code insights and analysis
        """
        with self.lock:
            if file_path not in self.file_index:
                return {"error": "File not indexed"}

            element_ids = self.file_index[file_path]
            elements = [self.code_elements[eid] for eid in element_ids]

            insights = {
                'file_path': file_path,
                'language': elements[0].language.value if elements else 'unknown',
                'total_elements': len(elements),
                'element_breakdown': {},
                'complexity_analysis': {},
                'quality_metrics': {},
                'suggestions': []
            }

            # Element breakdown
            for element in elements:
                element_type = element.element_type.value
                insights['element_breakdown'][element_type] = \
                    insights['element_breakdown'].get(element_type, 0) + 1

            # Complexity analysis
            complexities = [e.complexity_score for e in elements]
            if complexities:
                insights['complexity_analysis'] = {
                    'average_complexity': sum(complexities) / len(complexities),
                    'max_complexity': max(complexities),
                    'high_complexity_elements': [
                        e.name for e in elements if e.complexity_score > 0.7
                    ]
                }

            # Quality metrics
            insights['quality_metrics'] = {
                'documented_elements': len([e for e in elements if e.docstring]),
                'error': str(e),
                'source_language': source_language.value,
                'target_language': target_language.value
            }

    def get_code_insights(self, file_path: str) -> Dict[str, Any]:
        """Get comprehensive insights about a code file.

        Args:
            file_path: Path to the file to analyze

        Returns:
            Code insights and analysis
        """
        with self.lock:
            if file_path not in self.file_index:
                return {"error": "File not indexed"}

            element_ids = self.file_index[file_path]
            elements = [self.code_elements[eid] for eid in element_ids]

            insights = {
                'file_path': file_path,
                'language': elements[0].language.value if elements else 'unknown',
                'total_elements': len(elements),
                'element_breakdown': {},
                'complexity_analysis': {},
                'quality_metrics': {},
                'suggestions': []
            }

            # Element breakdown
            for element in elements:
                element_type = element.element_type.value
                insights['element_breakdown'][element_type] = \
                    insights['element_breakdown'].get(element_type, 0) + 1

            # Complexity analysis
            complexities = [e.complexity_score for e in elements]
            if complexities:
                insights['complexity_analysis'] = {
                    'average_complexity': sum(complexities) / len(complexities),
                    'max_complexity': max(complexities),
                    'high_complexity_elements': [
                        e.name for e in elements if e.complexity_score > 0.7
                    ]
                }

            # Quality metrics
            insights['quality_metrics'] = {
                'documented_elements': len([e for e in elements if e.docstring]),
                'documentation_ratio': len([e for e in elements if e.docstring]) / len(elements) if elements else 0,
                'average_function_length': self._calculate_average_function_length(elements),
                'naming_consistency': self._analyze_naming_consistency(elements)
            }

            # Generate suggestions
            insights['suggestions'] = self._generate_code_suggestions(elements)

        return insights

    def _find_code_files(self) -> List[str]:
        """Find all code files in the workspace."""
        code_files = []
        for ext in self.extension_to_language.keys():
            code_files.extend(self.workspace_dir.rglob(f"*{ext}"))
        return [str(f) for f in code_files if f.is_file()]

    def _index_file(self, file_path: str):
        """Index a single code file."""
        try:
            # Determine language
            file_ext = Path(file_path).suffix.lower()
            if file_ext not in self.extension_to_language:
                return

            language = self.extension_to_language[file_ext]

            # Read file content
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            # Process based on language
            if language in self.language_processors:
                elements = self.language_processors[language](content, file_path)

                # Store elements
                element_ids = []
                for element in elements:
                    self.code_elements[element.element_id] = element
                    element_ids.append(element.element_id)

                    # Update semantic index
                    for tag in element.semantic_tags:
                        if tag not in self.semantic_index:
                            self.semantic_index[tag] = set()
                        self.semantic_index[tag].add(element.element_id)

                # Update file index
                self.file_index[file_path] = element_ids

        except Exception as e:
            logger.error(f"Error indexing file {file_path}: {e}")

    def _process_python_code(self, content: str, file_path: str) -> List[CodeElement]:
        """Process Python code and extract elements."""
        elements = []

        try:
            tree = ast.parse(content)

            for node in ast.walk(tree):
                element = None

                if isinstance(node, ast.FunctionDef):
                    element = self._create_function_element(node, content, file_path, LanguageType.PYTHON)
                elif isinstance(node, ast.ClassDef):
                    element = self._create_class_element(node, content, file_path, LanguageType.PYTHON)
                elif isinstance(node, ast.Import) or isinstance(node, ast.ImportFrom):
                    element = self._create_import_element(node, content, file_path, LanguageType.PYTHON)

                if element:
                    elements.append(element)

        except SyntaxError as e:
            logger.warning(f"Syntax error in {file_path}: {e}")

        return elements

    def _process_javascript_code(self, content: str, file_path: str) -> List[CodeElement]:
        """Process JavaScript code and extract elements."""
        elements = []

        # Simple regex-based parsing for JavaScript
        # In production, would use a proper JS parser like esprima

        # Function declarations
        func_pattern = r'function\s+(\w+)\s*\([^)]*\)\s*\{'
        for match in re.finditer(func_pattern, content):
            element = CodeElement(
                element_id=str(uuid.uuid4()),
                name=match.group(1),
                element_type=CodeElementType.FUNCTION,
                language=LanguageType.JAVASCRIPT,
                file_path=file_path,
                line_start=content[:match.start()].count('\n') + 1,
                line_end=content[:match.end()].count('\n') + 1,
                column_start=match.start() - content.rfind('\n', 0, match.start()),
                column_end=match.end() - content.rfind('\n', 0, match.end()),
                signature=match.group(0),
                docstring=None,
                parameters=[],
                return_type=None,
                dependencies=[],
                complexity_score=0.5,
                semantic_tags=['function', 'javascript'],
                code_snippet=match.group(0),
                hash_signature=hashlib.md5(match.group(0).encode()).hexdigest()
            )
            elements.append(element)

        # Class declarations
        class_pattern = r'class\s+(\w+)(?:\s+extends\s+\w+)?\s*\{'
        for match in re.finditer(class_pattern, content):
            element = CodeElement(
                element_id=str(uuid.uuid4()),
                name=match.group(1),
                element_type=CodeElementType.CLASS,
                language=LanguageType.JAVASCRIPT,
                file_path=file_path,
                line_start=content[:match.start()].count('\n') + 1,
                line_end=content[:match.end()].count('\n') + 1,
                column_start=match.start() - content.rfind('\n', 0, match.start()),
                column_end=match.end() - content.rfind('\n', 0, match.end()),
                signature=match.group(0),
                docstring=None,
                parameters=[],
                return_type=None,
                dependencies=[],
                complexity_score=0.6,
                semantic_tags=['class', 'javascript'],
                code_snippet=match.group(0),
                hash_signature=hashlib.md5(match.group(0).encode()).hexdigest()
            )
            elements.append(element)

        return elements

    def _create_function_element(self, node: ast.FunctionDef, content: str, file_path: str, language: LanguageType) -> CodeElement:
        \"\"\"Create a function element from AST node.\"\"\"
        # Extract parameters
        parameters = [arg.arg for arg in node.args.args]

        # Extract docstring
        docstring = None
        if (node.body and isinstance(node.body[0], ast.Expr) and
            isinstance(node.body[0].value, ast.Constant) and
            isinstance(node.body[0].value.value, str)):
            docstring = node.body[0].value.value

        # Calculate complexity (simplified)
        complexity_score = self._calculate_complexity(node)

        # Extract dependencies
        dependencies = self._extract_dependencies(node)

        # Generate semantic tags
        semantic_tags = ['function', language.value]
        if 'test' in node.name.lower():
            semantic_tags.append('test')
        if node.name.startswith('_'):
            semantic_tags.append('private')

        return CodeElement(
            element_id=str(uuid.uuid4()),
            name=node.name,
            element_type=CodeElementType.FUNCTION,
            language=language,
            file_path=file_path,
            line_start=node.lineno,
            line_end=node.end_lineno or node.lineno,
            column_start=node.col_offset,
            column_end=node.end_col_offset or node.col_offset,
            signature=f"def {node.name}({', '.join(parameters)})",
            docstring=docstring,
            parameters=parameters,
            return_type=None,  # Would need type annotations
            dependencies=dependencies,
            complexity_score=complexity_score,
            semantic_tags=semantic_tags,
            code_snippet=ast.get_source_segment(content, node) or "",
            hash_signature=hashlib.md5(f"{node.name}{parameters}".encode()).hexdigest()
        )

    def _create_class_element(self, node: ast.ClassDef, content: str, file_path: str, language: LanguageType) -> CodeElement:
        \"\"\"Create a class element from AST node.\"\"\"
        # Extract base classes
        base_classes = [base.id if isinstance(base, ast.Name) else str(base) for base in node.bases]

        # Extract docstring
        docstring = None
        if (node.body and isinstance(node.body[0], ast.Expr) and
            isinstance(node.body[0].value, ast.Constant) and
            isinstance(node.body[0].value.value, str)):
            docstring = node.body[0].value.value

        # Calculate complexity
        complexity_score = len(node.body) / 20.0  # Simplified complexity

        # Generate semantic tags
        semantic_tags = ['class', language.value]
        if base_classes:
            semantic_tags.append('inheritance')

        return CodeElement(
            element_id=str(uuid.uuid4()),
            name=node.name,
            element_type=CodeElementType.CLASS,
            language=language,
            file_path=file_path,
            line_start=node.lineno,
            line_end=node.end_lineno or node.lineno,
            column_start=node.col_offset,
            column_end=node.end_col_offset or node.col_offset,
            signature=f"class {node.name}({', '.join(base_classes)})" if base_classes else f"class {node.name}",
            docstring=docstring,
            parameters=base_classes,
            return_type=None,
            dependencies=base_classes,
            complexity_score=min(complexity_score, 1.0),
            semantic_tags=semantic_tags,
            code_snippet=ast.get_source_segment(content, node) or "",
            hash_signature=hashlib.md5(f"{node.name}{base_classes}".encode()).hexdigest()
        )

    def _calculate_complexity(self, node: ast.AST) -> float:
        \"\"\"Calculate complexity score for an AST node.\"\"\"
        complexity = 1  # Base complexity

        for child in ast.walk(node):
            if isinstance(child, (ast.If, ast.While, ast.For, ast.Try)):
                complexity += 1
            elif isinstance(child, ast.FunctionDef):
                complexity += 0.5

        return min(complexity / 10.0, 1.0)  # Normalize to 0-1

    def _extract_dependencies(self, node: ast.AST) -> List[str]:
        \"\"\"Extract dependencies from an AST node.\"\"\"
        dependencies = []

        for child in ast.walk(node):
            if isinstance(child, ast.Call) and isinstance(child.func, ast.Name):
                dependencies.append(child.func.id)
            elif isinstance(child, ast.Name):
                dependencies.append(child.id)

        return list(set(dependencies))  # Remove duplicates
