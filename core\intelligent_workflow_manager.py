"""
Intelligent Workflow Manager
Provides context-aware conversation management, progress tracking, smart predictions,
and advanced error recovery mechanisms.
"""

import json
import time
import threading
import uuid
from pathlib import Path
from typing import Dict, List, Optional, Any, Callable, Set
from dataclasses import dataclass, field, asdict
from enum import Enum
import pickle
import sqlite3
from collections import defaultdict, deque

from utils import get_logger

logger = get_logger()

class WorkflowState(Enum):
    """Workflow execution states."""
    IDLE = "idle"
    PLANNING = "planning"
    EXECUTING = "executing"
    WAITING = "waiting"
    ERROR = "error"
    COMPLETED = "completed"
    CANCELLED = "cancelled"

class Priority(Enum):
    """Task priority levels."""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4

@dataclass
class WorkflowStep:
    """Individual workflow step."""
    step_id: str
    name: str
    description: str
    tool_name: str
    parameters: Dict[str, Any]
    dependencies: List[str] = field(default_factory=list)
    priority: Priority = Priority.NORMAL
    estimated_duration: float = 0.0
    actual_duration: Optional[float] = None
    status: WorkflowState = WorkflowState.IDLE
    result: Optional[Any] = None
    error_message: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 3
    created_at: float = field(default_factory=time.time)
    started_at: Optional[float] = None
    completed_at: Optional[float] = None

@dataclass
class WorkflowPlan:
    """Complete workflow plan."""
    plan_id: str
    name: str
    description: str
    steps: List[WorkflowStep] = field(default_factory=list)
    context: Dict[str, Any] = field(default_factory=dict)
    created_at: float = field(default_factory=time.time)
    estimated_total_duration: float = 0.0
    actual_total_duration: Optional[float] = None
    status: WorkflowState = WorkflowState.IDLE
    progress_percentage: float = 0.0

@dataclass
class ConversationContext:
    """Context for conversation management."""
    conversation_id: str
    user_intent: str
    domain: str  # e.g., "web_development", "data_analysis", "devops"
    complexity_level: int  # 1-10 scale
    current_workflow: Optional[WorkflowPlan] = None
    conversation_history: List[Dict[str, Any]] = field(default_factory=list)
    user_preferences: Dict[str, Any] = field(default_factory=dict)
    learned_patterns: Dict[str, Any] = field(default_factory=dict)
    success_metrics: Dict[str, float] = field(default_factory=dict)

class IntelligentWorkflowManager:
    """Intelligent workflow management system."""
    
    def __init__(self, workspace_dir: Path, db_path: Optional[Path] = None):
        self.workspace_dir = workspace_dir
        self.db_path = db_path or (workspace_dir / ".workflow_db.sqlite")
        self.lock = threading.RLock()
        
        # Active workflows and contexts
        self.active_workflows: Dict[str, WorkflowPlan] = {}
        self.conversation_contexts: Dict[str, ConversationContext] = {}
        
        # Learning and prediction systems
        self.pattern_database = defaultdict(list)
        self.success_patterns = defaultdict(float)
        self.error_patterns = defaultdict(list)
        
        # Execution queue and scheduler
        self.execution_queue = deque()
        self.running_steps: Set[str] = set()
        self.step_results: Dict[str, Any] = {}
        
        # Initialize database
        self._init_database()
        
        # Load historical data
        self._load_historical_data()
    
    def _init_database(self):
        """Initialize SQLite database for persistence."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS workflows (
                        plan_id TEXT PRIMARY KEY,
                        name TEXT,
                        description TEXT,
                        data BLOB,
                        created_at REAL,
                        status TEXT
                    )
                ''')
                
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS conversations (
                        conversation_id TEXT PRIMARY KEY,
                        user_intent TEXT,
                        domain TEXT,
                        data BLOB,
                        created_at REAL
                    )
                ''')
                
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS patterns (
                        pattern_id TEXT PRIMARY KEY,
                        pattern_type TEXT,
                        pattern_data BLOB,
                        success_rate REAL,
                        usage_count INTEGER,
                        created_at REAL
                    )
                ''')
                
                conn.commit()
                
        except Exception as e:
            logger.error(f"Error initializing database: {e}")
    
    def _load_historical_data(self):
        """Load historical patterns and data."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # Load patterns
                cursor = conn.execute('SELECT * FROM patterns')
                for row in cursor.fetchall():
                    pattern_id, pattern_type, pattern_data, success_rate, usage_count, created_at = row
                    data = pickle.loads(pattern_data)
                    self.pattern_database[pattern_type].append({
                        'id': pattern_id,
                        'data': data,
                        'success_rate': success_rate,
                        'usage_count': usage_count,
                        'created_at': created_at
                    })
                
        except Exception as e:
            logger.error(f"Error loading historical data: {e}")
    
    def create_conversation_context(self, user_input: str, conversation_id: Optional[str] = None) -> ConversationContext:
        """Create or update conversation context."""
        with self.lock:
            if conversation_id is None:
                conversation_id = str(uuid.uuid4())
            
            # Analyze user intent and domain
            domain = self._analyze_domain(user_input)
            complexity = self._estimate_complexity(user_input)
            
            context = ConversationContext(
                conversation_id=conversation_id,
                user_intent=user_input,
                domain=domain,
                complexity_level=complexity
            )
            
            # Add to conversation history
            context.conversation_history.append({
                'role': 'user',
                'content': user_input,
                'timestamp': time.time(),
                'domain': domain,
                'complexity': complexity
            })
            
            self.conversation_contexts[conversation_id] = context
            return context
    
    def _analyze_domain(self, user_input: str) -> str:
        """Analyze the domain/category of user input."""
        domain_keywords = {
            'web_development': ['website', 'html', 'css', 'javascript', 'react', 'vue', 'angular', 'frontend', 'backend'],
            'data_analysis': ['data', 'analysis', 'pandas', 'numpy', 'visualization', 'chart', 'graph', 'statistics'],
            'devops': ['deploy', 'docker', 'kubernetes', 'ci/cd', 'pipeline', 'infrastructure', 'server'],
            'machine_learning': ['ml', 'ai', 'model', 'training', 'prediction', 'neural', 'tensorflow', 'pytorch'],
            'mobile_development': ['mobile', 'android', 'ios', 'react native', 'flutter', 'app'],
            'database': ['database', 'sql', 'mongodb', 'postgresql', 'mysql', 'query'],
            'testing': ['test', 'unittest', 'pytest', 'testing', 'qa', 'quality'],
            'security': ['security', 'authentication', 'authorization', 'encryption', 'vulnerability']
        }
        
        user_input_lower = user_input.lower()
        domain_scores = {}
        
        for domain, keywords in domain_keywords.items():
            score = sum(1 for keyword in keywords if keyword in user_input_lower)
            if score > 0:
                domain_scores[domain] = score
        
        if domain_scores:
            return max(domain_scores, key=domain_scores.get)
        return 'general'
    
    def _estimate_complexity(self, user_input: str) -> int:
        """Estimate complexity level (1-10) based on user input."""
        complexity_indicators = {
            'simple': ['create', 'make', 'simple', 'basic', 'hello world'],
            'complext': ['implement', 'build', 'develop', 'integrate', 'configure'],
            'moderate': ['optimize', 'scale', 'architecture', 'performance', 'advanced', 'enterprise'],
            'very_complex': ['distributed', 'microservices', 'machine learning', 'ai', 'big data', 'real-time']
        }
        
        user_input_lower = user_input.lower()
        complexity_score = 1
        
        for level, indicators in complexity_indicators.items():
            if any(indicator in user_input_lower for indicator in indicators):
                if level == 'simple':
                    complexity_score = max(complexity_score, 2)
                elif level == 'moderate':
                    complexity_score = max(complexity_score, 5)
                elif level == 'complex':
                    complexity_score = max(complexity_score, 8)
                elif level == 'very_complex':
                    complexity_score = max(complexity_score, 10)
        
        # Adjust based on length and technical terms
        word_count = len(user_input.split())
        if word_count > 50:
            complexity_score += 1
        if word_count > 100:
            complexity_score += 1
        
        return min(complexity_score, 10)
    
    def generate_workflow_plan(self, context: ConversationContext, available_tools: List[str]) -> WorkflowPlan:
        """Generate intelligent workflow plan based on context."""
        with self.lock:
            plan_id = str(uuid.uuid4())
            
            # Create workflow plan based on domain and complexity
            plan = WorkflowPlan(
                plan_id=plan_id,
                name=f"Workflow for: {context.user_intent[:50]}...",
                description=f"Generated workflow for {context.domain} task",
                context={'conversation_id': context.conversation_id, 'domain': context.domain}
            )
            
            # Generate steps based on patterns and domain knowledge
            steps = self._generate_workflow_steps(context, available_tools)
            plan.steps = steps
            
            # Calculate estimated duration
            plan.estimated_total_duration = sum(step.estimated_duration for step in steps)
            
            # Store the plan
            self.active_workflows[plan_id] = plan
            context.current_workflow = plan
            
            # Save to database
            self._save_workflow_plan(plan)
            
            return plan
    
    def _generate_workflow_steps(self, context: ConversationContext, available_tools: List[str]) -> List[WorkflowStep]:
        """Generate workflow steps based on context and available tools."""
        steps = []
        
        # Domain-specific step generation
        if context.domain == 'web_development':
            steps.extend(self._generate_web_dev_steps(context, available_tools))
        elif context.domain == 'data_analysis':
            steps.extend(self._generate_data_analysis_steps(context, available_tools))
        elif context.domain == 'devops':
            steps.extend(self._generate_devops_steps(context, available_tools))
        else:
            steps.extend(self._generate_general_steps(context, available_tools))
        
        return steps
    
    def _generate_web_dev_steps(self, context: ConversationContext, available_tools: List[str]) -> List[WorkflowStep]:
        """Generate web development workflow steps."""
        steps = []
        
        # Common web development workflow
        if 'create_project' in available_tools:
            steps.append(WorkflowStep(
                step_id=str(uuid.uuid4()),
                name="Create Project Structure",
                description="Set up project structure and files",
                tool_name="create_project",
                parameters={'template': 'react' if 'react' in context.user_intent.lower() else 'javascript'},
                estimated_duration=30.0
            ))
        
        if 'file_write' in available_tools:
            steps.append(WorkflowStep(
                step_id=str(uuid.uuid4()),
                name="Create HTML Structure",
                description="Create basic HTML structure",
                tool_name="file_write",
                parameters={'path': 'index.html', 'content': '<!DOCTYPE html>...'},
                estimated_duration=15.0
            ))
        
        return steps
    
    def _generate_data_analysis_steps(self, context: ConversationContext, available_tools: List[str]) -> List[WorkflowStep]:
        """Generate data analysis workflow steps."""
        steps = []
        
        if 'create_project' in available_tools:
            steps.append(WorkflowStep(
                step_id=str(uuid.uuid4()),
                name="Create Data Analysis Project",
                description="Set up Python data analysis project",
                tool_name="create_project",
                parameters={'template': 'python'},
                estimated_duration=20.0
            ))
        
        return steps
    
    def _generate_devops_steps(self, context: ConversationContext, available_tools: List[str]) -> List[WorkflowStep]:
        """Generate DevOps workflow steps."""
        steps = []
        
        if 'generate_dockerfile' in available_tools:
            steps.append(WorkflowStep(
                step_id=str(uuid.uuid4()),
                name="Generate Dockerfile",
                description="Create Dockerfile for containerization",
                tool_name="generate_dockerfile",
                parameters={'project_type': 'python'},
                estimated_duration=10.0
            ))
        
        return steps
    
    def _generate_general_steps(self, context: ConversationContext, available_tools: List[str]) -> List[WorkflowStep]:
        """Generate general workflow steps."""
        steps = []
        
        # Basic analysis step
        if 'codebase_analyze' in available_tools:
            steps.append(WorkflowStep(
                step_id=str(uuid.uuid4()),
                name="Analyze Current State",
                description="Analyze current codebase or workspace",
                tool_name="codebase_analyze",
                parameters={},
                estimated_duration=20.0
            ))
        
        return steps

    def execute_workflow_step(self, step: WorkflowStep, tool_executor: Callable) -> bool:
        """Execute a single workflow step."""
        with self.lock:
            try:
                step.status = WorkflowState.EXECUTING
                step.started_at = time.time()

                # Execute the tool
                result = tool_executor(step.tool_name, step.parameters)

                step.result = result
                step.status = WorkflowState.COMPLETED
                step.completed_at = time.time()
                step.actual_duration = step.completed_at - step.started_at

                # Store result for dependent steps
                self.step_results[step.step_id] = result

                # Learn from successful execution
                self._record_success_pattern(step)

                return True

            except Exception as e:
                step.error_message = str(e)
                step.status = WorkflowState.ERROR
                step.completed_at = time.time()

                # Record error pattern
                self._record_error_pattern(step, e)

                # Attempt recovery
                if step.retry_count < step.max_retries:
                    step.retry_count += 1
                    step.status = WorkflowState.WAITING
                    return False

                return False

    def _record_success_pattern(self, step: WorkflowStep):
        """Record successful execution pattern for learning."""
        pattern_key = f"{step.tool_name}_{step.parameters.get('template', 'default')}"
        self.success_patterns[pattern_key] += 1

        # Store detailed pattern
        pattern_data = {
            'tool_name': step.tool_name,
            'parameters': step.parameters,
            'duration': step.actual_duration,
            'context': step.description
        }

        self.pattern_database['success'].append({
            'id': str(uuid.uuid4()),
            'data': pattern_data,
            'success_rate': 1.0,
            'usage_count': 1,
            'created_at': time.time()
        })

    def _record_error_pattern(self, step: WorkflowStep, error: Exception):
        """Record error pattern for learning and recovery."""
        error_data = {
            'tool_name': step.tool_name,
            'parameters': step.parameters,
            'error_type': type(error).__name__,
            'error_message': str(error),
            'retry_count': step.retry_count
        }

        self.error_patterns[step.tool_name].append(error_data)

    def predict_next_steps(self, context: ConversationContext, current_step: Optional[WorkflowStep] = None) -> List[str]:
        """Predict next logical steps based on context and patterns."""
        predictions = []

        # Use domain knowledge
        if context.domain == 'web_development':
            if current_step and current_step.tool_name == 'create_project':
                predictions.extend([
                    "Set up development environment",
                    "Create component structure",
                    "Implement routing",
                    "Add styling and CSS"
                ])
            elif not current_step:
                predictions.extend([
                    "Create project structure",
                    "Set up package.json",
                    "Initialize Git repository"
                ])

        elif context.domain == 'data_analysis':
            predictions.extend([
                "Load and explore data",
                "Clean and preprocess data",
                "Perform statistical analysis",
                "Create visualizations",
                "Generate insights report"
            ])

        elif context.domain == 'devops':
            predictions.extend([
                "Create Dockerfile",
                "Set up CI/CD pipeline",
                "Configure deployment",
                "Set up monitoring"
            ])

        # Use learned patterns
        if current_step:
            similar_patterns = self._find_similar_patterns(current_step)
            for pattern in similar_patterns[:3]:  # Top 3 similar patterns
                if 'next_steps' in pattern.get('data', {}):
                    predictions.extend(pattern['data']['next_steps'])

        return list(dict.fromkeys(predictions))  # Remove duplicates while preserving order

    def _find_similar_patterns(self, step: WorkflowStep) -> List[Dict[str, Any]]:
        """Find similar execution patterns from history."""
        similar_patterns = []

        for pattern_type, patterns in self.pattern_database.items():
            for pattern in patterns:
                if pattern['data'].get('tool_name') == step.tool_name:
                    similarity_score = self._calculate_similarity(step, pattern['data'])
                    if similarity_score > 0.7:  # 70% similarity threshold
                        pattern['similarity_score'] = similarity_score
                        similar_patterns.append(pattern)

        # Sort by similarity score and success rate
        similar_patterns.sort(key=lambda x: (x.get('similarity_score', 0), x.get('success_rate', 0)), reverse=True)
        return similar_patterns

    def _calculate_similarity(self, step: WorkflowStep, pattern_data: Dict[str, Any]) -> float:
        """Calculate similarity between current step and historical pattern."""
        similarity = 0.0

        # Tool name match
        if step.tool_name == pattern_data.get('tool_name'):
            similarity += 0.4

        # Parameter similarity
        step_params = set(step.parameters.keys())
        pattern_params = set(pattern_data.get('parameters', {}).keys())

        if step_params and pattern_params:
            param_similarity = len(step_params & pattern_params) / len(step_params | pattern_params)
            similarity += 0.3 * param_similarity

        # Context similarity (basic keyword matching)
        step_words = set(step.description.lower().split())
        pattern_words = set(pattern_data.get('context', '').lower().split())

        if step_words and pattern_words:
            context_similarity = len(step_words & pattern_words) / len(step_words | pattern_words)
            similarity += 0.3 * context_similarity

        return similarity

    def get_workflow_progress(self, plan_id: str) -> Dict[str, Any]:
        """Get detailed workflow progress information."""
        with self.lock:
            if plan_id not in self.active_workflows:
                return {'error': 'Workflow not found'}

            plan = self.active_workflows[plan_id]

            completed_steps = [s for s in plan.steps if s.status == WorkflowState.COMPLETED]
            failed_steps = [s for s in plan.steps if s.status == WorkflowState.ERROR]
            running_steps = [s for s in plan.steps if s.status == WorkflowState.EXECUTING]

            progress_percentage = len(completed_steps) / len(plan.steps) * 100 if plan.steps else 0

            return {
                'plan_id': plan_id,
                'name': plan.name,
                'status': plan.status.value,
                'progress_percentage': progress_percentage,
                'total_steps': len(plan.steps),
                'completed_steps': len(completed_steps),
                'failed_steps': len(failed_steps),
                'running_steps': len(running_steps),
                'estimated_duration': plan.estimated_total_duration,
                'actual_duration': plan.actual_total_duration,
                'steps': [
                    {
                        'step_id': step.step_id,
                        'name': step.name,
                        'status': step.status.value,
                        'progress': 100 if step.status == WorkflowState.COMPLETED else 0,
                        'error': step.error_message
                    }
                    for step in plan.steps
                ]
            }

    def suggest_optimizations(self, plan_id: str) -> List[str]:
        """Suggest workflow optimizations based on execution data."""
        suggestions = []

        with self.lock:
            if plan_id not in self.active_workflows:
                return suggestions

            plan = self.active_workflows[plan_id]

            # Analyze step durations
            slow_steps = [s for s in plan.steps if s.actual_duration and s.actual_duration > s.estimated_duration * 2]
            if slow_steps:
                suggestions.append(f"Consider optimizing {len(slow_steps)} slow-running steps")

            # Analyze error patterns
            error_steps = [s for s in plan.steps if s.status == WorkflowState.ERROR]
            if error_steps:
                suggestions.append(f"Review {len(error_steps)} failed steps for common issues")

            # Suggest parallelization
            independent_steps = [s for s in plan.steps if not s.dependencies]
            if len(independent_steps) > 1:
                suggestions.append("Consider running independent steps in parallel")

            # Suggest caching
            repeated_operations = defaultdict(int)
            for step in plan.steps:
                key = f"{step.tool_name}_{json.dumps(step.parameters, sort_keys=True)}"
                repeated_operations[key] += 1

            repeated = [k for k, v in repeated_operations.items() if v > 1]
            if repeated:
                suggestions.append("Consider caching results for repeated operations")

        return suggestions

    def _save_workflow_plan(self, plan: WorkflowPlan):
        """Save workflow plan to database."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                data = pickle.dumps(asdict(plan))
                conn.execute(
                    'INSERT OR REPLACE INTO workflows (plan_id, name, description, data, created_at, status) VALUES (?, ?, ?, ?, ?, ?)',
                    (plan.plan_id, plan.name, plan.description, data, plan.created_at, plan.status.value)
                )
                conn.commit()
        except Exception as e:
            logger.error(f"Error saving workflow plan: {e}")

    def get_conversation_insights(self, conversation_id: str) -> Dict[str, Any]:
        """Get insights about conversation patterns and user preferences."""
        with self.lock:
            if conversation_id not in self.conversation_contexts:
                return {'error': 'Conversation not found'}

            context = self.conversation_contexts[conversation_id]

            # Analyze conversation patterns
            message_count = len(context.conversation_history)
            avg_complexity = sum(msg.get('complexity', 1) for msg in context.conversation_history) / message_count if message_count else 1

            # Domain distribution
            domains = [msg.get('domain', 'general') for msg in context.conversation_history]
            domain_counts = defaultdict(int)
            for domain in domains:
                domain_counts[domain] += 1

            return {
                'conversation_id': conversation_id,
                'message_count': message_count,
                'primary_domain': context.domain,
                'average_complexity': avg_complexity,
                'domain_distribution': dict(domain_counts),
                'user_preferences': context.user_preferences,
                'success_metrics': context.success_metrics
            }

    def recover_from_error(self, step: WorkflowStep) -> Optional[WorkflowStep]:
        """Attempt to recover from step execution error."""
        if step.status != WorkflowState.ERROR:
            return None

        # Look for similar error patterns and recovery strategies
        similar_errors = []
        for error_data in self.error_patterns.get(step.tool_name, []):
            if error_data['error_type'] == step.error_message.split(':')[0]:
                similar_errors.append(error_data)

        if similar_errors:
            # Try common recovery strategies
            recovery_step = WorkflowStep(
                step_id=str(uuid.uuid4()),
                name=f"Recovery for {step.name}",
                description=f"Attempting recovery from {step.error_message}",
                tool_name=step.tool_name,
                parameters=step.parameters.copy(),
                priority=Priority.HIGH
            )

            # Modify parameters based on error patterns
            if 'timeout' in step.error_message.lower():
                recovery_step.parameters['timeout'] = recovery_step.parameters.get('timeout', 30) * 2

            if 'permission' in step.error_message.lower():
                recovery_step.parameters['force'] = True

            return recovery_step

        return None
