"""
Full-Stack Project Manager
Comprehensive project management with multi-language support, dependency management,
testing framework integration, and deployment automation.
"""

import os
import json
import subprocess
import time
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
import shutil
import tempfile

from utils import get_logger

logger = get_logger()

class ProjectType(Enum):
    """Supported project types."""
    PYTHON_WEB = "python_web"
    PYTHON_API = "python_api"
    PYTHON_CLI = "python_cli"
    JAVASCRIPT_WEB = "javascript_web"
    JAVASCRIPT_API = "javascript_api"
    REACT_APP = "react_app"
    VUE_APP = "vue_app"
    ANGULAR_APP = "angular_app"
    NODEJS_API = "nodejs_api"
    FLASK_API = "flask_api"
    FASTAPI_API = "fastapi_api"
    DJANGO_WEB = "django_web"
    RUST_CLI = "rust_cli"
    RUST_WEB = "rust_web"
    GO_API = "go_api"
    GO_CLI = "go_cli"
    JAVA_SPRING = "java_spring"
    CSHARP_WEB = "csharp_web"
    MOBILE_REACT_NATIVE = "mobile_react_native"
    MOBILE_FLUTTER = "mobile_flutter"

class DeploymentTarget(Enum):
    """Deployment targets."""
    LOCAL = "local"
    DOCKER = "docker"
    KUBERNETES = "kubernetes"
    HEROKU = "heroku"
    VERCEL = "vercel"
    NETLIFY = "netlify"
    AWS = "aws"
    GCP = "gcp"
    AZURE = "azure"

@dataclass
class ProjectConfig:
    """Project configuration."""
    name: str
    project_type: ProjectType
    description: str = ""
    author: str = ""
    version: str = "1.0.0"
    license: str = "MIT"
    dependencies: List[str] = field(default_factory=list)
    dev_dependencies: List[str] = field(default_factory=list)
    features: List[str] = field(default_factory=list)
    deployment_targets: List[DeploymentTarget] = field(default_factory=list)
    testing_framework: str = "default"
    ci_cd: bool = True
    docker_support: bool = True
    database: Optional[str] = None
    authentication: bool = False
    api_documentation: bool = True

@dataclass
class ProjectStructure:
    """Project structure definition."""
    directories: List[str] = field(default_factory=list)
    files: Dict[str, str] = field(default_factory=dict)  # path -> content
    templates: Dict[str, str] = field(default_factory=dict)  # template_name -> content

class FullStackProjectManager:
    """Comprehensive full-stack project manager."""
    
    def __init__(self, workspace_dir: Path):
        self.workspace_dir = workspace_dir
        self.templates_dir = workspace_dir / ".templates"
        self.templates_dir.mkdir(exist_ok=True)
        
        # Initialize project templates
        self.project_templates = self._initialize_templates()
        
        # Dependency managers
        self.dependency_managers = {
            'python': self._manage_python_dependencies,
            'javascript': self._manage_javascript_dependencies,
            'rust': self._manage_rust_dependencies,
            'go': self._manage_go_dependencies,
            'java': self._manage_java_dependencies,
            'csharp': self._manage_csharp_dependencies
        }
    
    def create_fullstack_project(self, config: ProjectConfig) -> Dict[str, Any]:
        """Create a complete full-stack project."""
        try:
            project_path = self.workspace_dir / config.name
            if project_path.exists():
                return {
                    'success': False,
                    'error': f'Project {config.name} already exists'
                }
            
            # Create project directory
            project_path.mkdir(parents=True)
            
            # Generate project structure
            structure = self._generate_project_structure(config)
            
            # Create directories
            for directory in structure.directories:
                (project_path / directory).mkdir(parents=True, exist_ok=True)
            
            # Create files
            created_files = []
            for file_path, content in structure.files.items():
                full_path = project_path / file_path
                full_path.parent.mkdir(parents=True, exist_ok=True)
                with open(full_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                created_files.append(str(full_path))
            
            # Install dependencies
            dependency_result = self._install_dependencies(project_path, config)
            
            # Set up testing framework
            testing_result = self._setup_testing(project_path, config)
            
            # Generate deployment files
            deployment_result = self._generate_deployment_files(project_path, config)
            
            # Set up CI/CD
            cicd_result = self._setup_cicd(project_path, config) if config.ci_cd else {'success': True}
            
            # Initialize Git repository
            git_result = self._initialize_git(project_path)
            
            return {
                'success': True,
                'project_name': config.name,
                'project_path': str(project_path),
                'project_type': config.project_type.value,
                'created_files': created_files,
                'dependencies': dependency_result,
                'testing': testing_result,
                'deployment': deployment_result,
                'cicd': cicd_result,
                'git': git_result,
                'next_steps': self._get_next_steps(config)
            }
            
        except Exception as e:
            logger.error(f"Error creating full-stack project: {e}")
            return {
                'success': False,
                'error': str(e),
                'project_name': config.name
            }
    
    def _initialize_templates(self) -> Dict[ProjectType, ProjectStructure]:
        """Initialize project templates for different types."""
        templates = {}
        
        # Python Web Application
        templates[ProjectType.PYTHON_WEB] = ProjectStructure(
            directories=[
                'app', 'app/templates', 'app/static', 'app/static/css', 
                'app/static/js', 'tests', 'docs', 'scripts'
            ],
            files={
                'app/__init__.py': self._get_python_web_init(),
                'app/main.py': self._get_python_web_main(),
                'app/models.py': self._get_python_models(),
                'app/routes.py': self._get_python_routes(),
                'app/templates/base.html': self._get_base_html_template(),
                'app/templates/index.html': self._get_index_html_template(),
                'app/static/css/style.css': self._get_base_css(),
                'requirements.txt': 'flask==2.3.3\nwerkzeug==2.3.7\nflask-sqlalchemy==3.0.5\nflask-migrate==4.0.5',
                'config.py': self._get_python_config(),
                'run.py': self._get_python_run_script(),
                'tests/test_app.py': self._get_python_tests(),
                'README.md': self._get_readme_template('Python Web Application'),
                '.env.example': self._get_env_template(),
                '.gitignore': self._get_python_gitignore()
            }
        )
        
        # React Application
        templates[ProjectType.REACT_APP] = ProjectStructure(
            directories=[
                'src', 'src/components', 'src/pages', 'src/hooks', 
                'src/utils', 'src/styles', 'public', 'tests'
            ],
            files={
                'package.json': self._get_react_package_json(),
                'src/index.js': self._get_react_index(),
                'src/App.js': self._get_react_app(),
                'src/App.css': self._get_react_app_css(),
                'src/components/Header.js': self._get_react_header_component(),
                'src/pages/Home.js': self._get_react_home_page(),
                'public/index.html': self._get_react_html(),
                'public/manifest.json': self._get_react_manifest(),
                'README.md': self._get_readme_template('React Application'),
                '.gitignore': self._get_javascript_gitignore(),
                '.env.example': 'REACT_APP_API_URL=http://localhost:3001'
            }
        )
        
        # FastAPI Application
        templates[ProjectType.FASTAPI_API] = ProjectStructure(
            directories=[
                'app', 'app/api', 'app/api/v1', 'app/core', 'app/models', 
                'app/schemas', 'app/crud', 'tests', 'alembic'
            ],
            files={
                'app/__init__.py': '',
                'app/main.py': self._get_fastapi_main(),
                'app/api/__init__.py': '',
                'app/api/v1/__init__.py': '',
                'app/api/v1/endpoints.py': self._get_fastapi_endpoints(),
                'app/core/config.py': self._get_fastapi_config(),
                'app/core/security.py': self._get_fastapi_security(),
                'app/models/__init__.py': '',
                'app/schemas/__init__.py': '',
                'app/crud/__init__.py': '',
                'requirements.txt': self._get_fastapi_requirements(),
                'tests/test_main.py': self._get_fastapi_tests(),
                'README.md': self._get_readme_template('FastAPI Application'),
                'Dockerfile': self._get_fastapi_dockerfile(),
                '.env.example': self._get_fastapi_env(),
                '.gitignore': self._get_python_gitignore()
            }
        )
        
        # Node.js API
        templates[ProjectType.NODEJS_API] = ProjectStructure(
            directories=[
                'src', 'src/controllers', 'src/models', 'src/routes', 
                'src/middleware', 'src/utils', 'tests', 'docs'
            ],
            files={
                'package.json': self._get_nodejs_package_json(),
                'src/app.js': self._get_nodejs_app(),
                'src/server.js': self._get_nodejs_server(),
                'src/controllers/userController.js': self._get_nodejs_controller(),
                'src/routes/userRoutes.js': self._get_nodejs_routes(),
                'src/middleware/auth.js': self._get_nodejs_middleware(),
                'src/utils/database.js': self._get_nodejs_database(),
                'tests/user.test.js': self._get_nodejs_tests(),
                'README.md': self._get_readme_template('Node.js API'),
                'Dockerfile': self._get_nodejs_dockerfile(),
                '.env.example': self._get_nodejs_env(),
                '.gitignore': self._get_javascript_gitignore()
            }
        )
        
        return templates
    
    def _generate_project_structure(self, config: ProjectConfig) -> ProjectStructure:
        """Generate project structure based on configuration."""
        base_structure = self.project_templates.get(config.project_type)
        if not base_structure:
            raise ValueError(f"Unsupported project type: {config.project_type}")
        
        # Clone the base structure
        structure = ProjectStructure(
            directories=base_structure.directories.copy(),
            files=base_structure.files.copy(),
            templates=base_structure.templates.copy()
        )
        
        # Customize based on features
        if 'authentication' in config.features or config.authentication:
            self._add_authentication_files(structure, config)
        
        if 'database' in config.features or config.database:
            self._add_database_files(structure, config)
        
        if 'api_documentation' in config.features or config.api_documentation:
            self._add_api_documentation(structure, config)
        
        # Replace placeholders in file contents
        for file_path, content in structure.files.items():
            structure.files[file_path] = self._replace_placeholders(content, config)
        
        return structure
    
    def _replace_placeholders(self, content: str, config: ProjectConfig) -> str:
        """Replace placeholders in file content."""
        replacements = {
            '{{PROJECT_NAME}}': config.name,
            '{{PROJECT_DESCRIPTION}}': config.description,
            '{{AUTHOR}}': config.author,
            '{{VERSION}}': config.version,
            '{{LICENSE}}': config.license
        }
        
        for placeholder, value in replacements.items():
            content = content.replace(placeholder, value)
        
        return content

    def _get_python_web_init(self) -> str:
        """Get Python web application __init__.py."""
        return '''from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate

db = SQLAlchemy()
migrate = Migrate()

def create_app():
    app = Flask(__name__)
    app.config.from_object('config.Config')

    db.init_app(app)
    migrate.init_app(app, db)

    from app.routes import main
    app.register_blueprint(main)

    return app
'''

    def _get_python_web_main(self) -> str:
        """Get Python web application main.py."""
        return '''from app import create_app

app = create_app()

if __name__ == '__main__':
    app.run(debug=True)
'''

    def _get_python_models(self) -> str:
        """Get Python models template."""
        return '''from app import db
from datetime import datetime

class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<User {self.username}>'
'''

    def _get_python_routes(self) -> str:
        """Get Python routes template."""
        return '''from flask import Blueprint, render_template, request, jsonify
from app.models import User
from app import db

main = Blueprint('main', __name__)

@main.route('/')
def index():
    return render_template('index.html', title='{{PROJECT_NAME}}')

@main.route('/api/users', methods=['GET'])
def get_users():
    users = User.query.all()
    return jsonify([{'id': u.id, 'username': u.username, 'email': u.email} for u in users])

@main.route('/api/users', methods=['POST'])
def create_user():
    data = request.get_json()
    user = User(username=data['username'], email=data['email'])
    db.session.add(user)
    db.session.commit()
    return jsonify({'id': user.id, 'username': user.username, 'email': user.email}), 201
'''

    def _get_base_html_template(self) -> str:
        """Get base HTML template."""
        return '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{PROJECT_NAME}}{% endblock %}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <header>
        <nav>
            <h1>{{PROJECT_NAME}}</h1>
        </nav>
    </header>

    <main>
        {% block content %}{% endblock %}
    </main>

    <footer>
        <p>&copy; 2024 {{PROJECT_NAME}}. All rights reserved.</p>
    </footer>

    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
</body>
</html>
'''

    def _get_index_html_template(self) -> str:
        """Get index HTML template."""
        return '''{% extends "base.html" %}

{% block content %}
<div class="container">
    <h2>Welcome to {{PROJECT_NAME}}</h2>
    <p>{{PROJECT_DESCRIPTION}}</p>

    <div class="features">
        <h3>Features</h3>
        <ul>
            <li>Modern web application</li>
            <li>RESTful API</li>
            <li>Database integration</li>
            <li>Responsive design</li>
        </ul>
    </div>
</div>
{% endblock %}
'''

    def _get_base_css(self) -> str:
        """Get base CSS template."""
        return '''/* Base styles for {{PROJECT_NAME}} */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f4f4f4;
}

header {
    background: #333;
    color: white;
    padding: 1rem 0;
}

nav h1 {
    text-align: center;
}

main {
    min-height: calc(100vh - 120px);
    padding: 2rem 0;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

footer {
    background: #333;
    color: white;
    text-align: center;
    padding: 1rem 0;
}

.features ul {
    list-style-type: none;
    padding: 1rem 0;
}

.features li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #ddd;
}
'''

    def _get_python_config(self) -> str:
        """Get Python configuration template."""
        return '''import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key'
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///app.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
'''

    def _get_python_run_script(self) -> str:
        """Get Python run script."""
        return '''#!/usr/bin/env python3
from app import create_app

app = create_app()

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)
'''

    def _get_python_tests(self) -> str:
        """Get Python test template."""
        return '''import unittest
from app import create_app, db
from app.models import User

class TestApp(unittest.TestCase):
    def setUp(self):
        self.app = create_app()
        self.app.config['TESTING'] = True
        self.app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
        self.client = self.app.test_client()

        with self.app.app_context():
            db.create_all()

    def tearDown(self):
        with self.app.app_context():
            db.session.remove()
            db.drop_all()

    def test_index_page(self):
        response = self.client.get('/')
        self.assertEqual(response.status_code, 200)

    def test_create_user(self):
        response = self.client.post('/api/users',
                                  json={'username': 'testuser', 'email': '<EMAIL>'})
        self.assertEqual(response.status_code, 201)

if __name__ == '__main__':
    unittest.main()
'''

    def _get_readme_template(self, project_type: str) -> str:
        """Get README template."""
        return f'''# {{{{PROJECT_NAME}}}}

{{{{PROJECT_DESCRIPTION}}}}

## {project_type}

### Features

- Modern architecture
- RESTful API
- Database integration
- Testing framework
- CI/CD ready
- Docker support

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd {{{{PROJECT_NAME}}}}

# Install dependencies
# (See specific instructions below based on technology)

# Run the application
# (See specific instructions below)
```

### Development

```bash
# Run in development mode
# (Technology-specific commands)

# Run tests
# (Technology-specific commands)

# Build for production
# (Technology-specific commands)
```

### Deployment

This project includes Docker support and CI/CD configuration for easy deployment.

### License

{{{{LICENSE}}}}
'''

    def _get_env_template(self) -> str:
        """Get environment template."""
        return '''# Environment variables
SECRET_KEY=your-secret-key-here
DATABASE_URL=sqlite:///app.db
DEBUG=True
'''

    def _get_python_gitignore(self) -> str:
        """Get Python .gitignore."""
        return '''# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo

# Environment variables
.env
.env.local

# Database
*.db
*.sqlite3

# Logs
*.log

# OS
.DS_Store
Thumbs.db
'''

    def _get_javascript_gitignore(self) -> str:
        """Get JavaScript .gitignore."""
        return '''# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Production build
build/
dist/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/

# OS
.DS_Store
Thumbs.db

# Logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
'''

    def _install_dependencies(self, project_path: Path, config: ProjectConfig) -> Dict[str, Any]:
        """Install project dependencies."""
        try:
            language = self._get_project_language(config.project_type)
            if language in self.dependency_managers:
                return self.dependency_managers[language](project_path, config)
            else:
                return {'success': True, 'message': 'No dependency manager for this project type'}
        except Exception as e:
            logger.error(f"Error installing dependencies: {e}")
            return {'success': False, 'error': str(e)}

    def _get_project_language(self, project_type: ProjectType) -> str:
        """Get the primary language for a project type."""
        language_map = {
            ProjectType.PYTHON_WEB: 'python',
            ProjectType.PYTHON_API: 'python',
            ProjectType.PYTHON_CLI: 'python',
            ProjectType.FLASK_API: 'python',
            ProjectType.FASTAPI_API: 'python',
            ProjectType.DJANGO_WEB: 'python',
            ProjectType.JAVASCRIPT_WEB: 'javascript',
            ProjectType.JAVASCRIPT_API: 'javascript',
            ProjectType.REACT_APP: 'javascript',
            ProjectType.VUE_APP: 'javascript',
            ProjectType.ANGULAR_APP: 'javascript',
            ProjectType.NODEJS_API: 'javascript',
            ProjectType.MOBILE_REACT_NATIVE: 'javascript',
            ProjectType.RUST_CLI: 'rust',
            ProjectType.RUST_WEB: 'rust',
            ProjectType.GO_API: 'go',
            ProjectType.GO_CLI: 'go',
            ProjectType.JAVA_SPRING: 'java',
            ProjectType.CSHARP_WEB: 'csharp',
            ProjectType.MOBILE_FLUTTER: 'dart'
        }
        return language_map.get(project_type, 'unknown')

    def _manage_python_dependencies(self, project_path: Path, config: ProjectConfig) -> Dict[str, Any]:
        """Manage Python dependencies."""
        try:
            # Create virtual environment
            venv_path = project_path / 'venv'
            subprocess.run(['python', '-m', 'venv', str(venv_path)], check=True, cwd=project_path)

            # Determine pip executable
            if os.name == 'nt':  # Windows
                pip_exe = venv_path / 'Scripts' / 'pip.exe'
            else:  # Unix-like
                pip_exe = venv_path / 'bin' / 'pip'

            # Install requirements
            requirements_file = project_path / 'requirements.txt'
            if requirements_file.exists():
                subprocess.run([str(pip_exe), 'install', '-r', 'requirements.txt'],
                             check=True, cwd=project_path)

            # Install additional dependencies
            if config.dependencies:
                subprocess.run([str(pip_exe), 'install'] + config.dependencies,
                             check=True, cwd=project_path)

            return {
                'success': True,
                'virtual_env': str(venv_path),
                'installed_packages': config.dependencies
            }

        except subprocess.CalledProcessError as e:
            return {'success': False, 'error': f'Failed to install Python dependencies: {e}'}
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def _manage_javascript_dependencies(self, project_path: Path, config: ProjectConfig) -> Dict[str, Any]:
        """Manage JavaScript dependencies."""
        try:
            # Check if package.json exists
            package_json = project_path / 'package.json'
            if not package_json.exists():
                return {'success': False, 'error': 'package.json not found'}

            # Install dependencies using npm
            subprocess.run(['npm', 'install'], check=True, cwd=project_path)

            # Install additional dependencies
            if config.dependencies:
                subprocess.run(['npm', 'install'] + config.dependencies,
                             check=True, cwd=project_path)

            # Install dev dependencies
            if config.dev_dependencies:
                subprocess.run(['npm', 'install', '--save-dev'] + config.dev_dependencies,
                             check=True, cwd=project_path)

            return {
                'success': True,
                'package_manager': 'npm',
                'installed_packages': config.dependencies,
                'dev_packages': config.dev_dependencies
            }

        except subprocess.CalledProcessError as e:
            return {'success': False, 'error': f'Failed to install JavaScript dependencies: {e}'}
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def _manage_rust_dependencies(self, project_path: Path, config: ProjectConfig) -> Dict[str, Any]:
        """Manage Rust dependencies."""
        try:
            # Check if Cargo.toml exists
            cargo_toml = project_path / 'Cargo.toml'
            if not cargo_toml.exists():
                return {'success': False, 'error': 'Cargo.toml not found'}

            # Build the project (this will download dependencies)
            subprocess.run(['cargo', 'build'], check=True, cwd=project_path)

            return {'success': True, 'package_manager': 'cargo'}

        except subprocess.CalledProcessError as e:
            return {'success': False, 'error': f'Failed to build Rust project: {e}'}
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def _manage_go_dependencies(self, project_path: Path, config: ProjectConfig) -> Dict[str, Any]:
        """Manage Go dependencies."""
        try:
            # Check if go.mod exists
            go_mod = project_path / 'go.mod'
            if not go_mod.exists():
                return {'success': False, 'error': 'go.mod not found'}

            # Download dependencies
            subprocess.run(['go', 'mod', 'download'], check=True, cwd=project_path)
            subprocess.run(['go', 'mod', 'tidy'], check=True, cwd=project_path)

            return {'success': True, 'package_manager': 'go mod'}

        except subprocess.CalledProcessError as e:
            return {'success': False, 'error': f'Failed to manage Go dependencies: {e}'}
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def _manage_java_dependencies(self, project_path: Path, config: ProjectConfig) -> Dict[str, Any]:
        """Manage Java dependencies."""
        try:
            # Check for Maven or Gradle
            pom_xml = project_path / 'pom.xml'
            build_gradle = project_path / 'build.gradle'

            if pom_xml.exists():
                subprocess.run(['mvn', 'dependency:resolve'], check=True, cwd=project_path)
                return {'success': True, 'package_manager': 'maven'}
            elif build_gradle.exists():
                subprocess.run(['gradle', 'build'], check=True, cwd=project_path)
                return {'success': True, 'package_manager': 'gradle'}
            else:
                return {'success': False, 'error': 'No Maven or Gradle configuration found'}

        except subprocess.CalledProcessError as e:
            return {'success': False, 'error': f'Failed to manage Java dependencies: {e}'}
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def _manage_csharp_dependencies(self, project_path: Path, config: ProjectConfig) -> Dict[str, Any]:
        """Manage C# dependencies."""
        try:
            # Check for .csproj files
            csproj_files = list(project_path.glob('*.csproj'))
            if not csproj_files:
                return {'success': False, 'error': 'No .csproj file found'}

            # Restore packages
            subprocess.run(['dotnet', 'restore'], check=True, cwd=project_path)

            return {'success': True, 'package_manager': 'dotnet'}

        except subprocess.CalledProcessError as e:
            return {'success': False, 'error': f'Failed to restore .NET packages: {e}'}
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def _setup_testing(self, project_path: Path, config: ProjectConfig) -> Dict[str, Any]:
        """Set up testing framework."""
        try:
            language = self._get_project_language(config.project_type)

            if language == 'python':
                return self._setup_python_testing(project_path, config)
            elif language == 'javascript':
                return self._setup_javascript_testing(project_path, config)
            else:
                return {'success': True, 'message': 'Testing setup not implemented for this language'}

        except Exception as e:
            return {'success': False, 'error': str(e)}

    def _setup_python_testing(self, project_path: Path, config: ProjectConfig) -> Dict[str, Any]:
        """Set up Python testing."""
        try:
            # Create pytest configuration
            pytest_ini = project_path / 'pytest.ini'
            with open(pytest_ini, 'w') as f:
                f.write('''[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = -v --tb=short
''')

            # Add pytest to requirements
            requirements_file = project_path / 'requirements-dev.txt'
            with open(requirements_file, 'w') as f:
                f.write('pytest==7.4.3\npytest-cov==4.1.0\npytest-mock==3.12.0\n')

            return {'success': True, 'framework': 'pytest', 'config_file': 'pytest.ini'}

        except Exception as e:
            return {'success': False, 'error': str(e)}

    def _setup_javascript_testing(self, project_path: Path, config: ProjectConfig) -> Dict[str, Any]:
        """Set up JavaScript testing."""
        try:
            # Create Jest configuration
            jest_config = project_path / 'jest.config.js'
            with open(jest_config, 'w') as f:
                f.write('''module.exports = {
  testEnvironment: 'node',
  collectCoverageFrom: [
    'src/**/*.{js,jsx}',
    '!src/index.js'
  ],
  coverageDirectory: 'coverage',
  testMatch: [
    '<rootDir>/tests/**/*.test.{js,jsx}',
    '<rootDir>/src/**/*.test.{js,jsx}'
  ]
};
''')

            return {'success': True, 'framework': 'jest', 'config_file': 'jest.config.js'}

        except Exception as e:
            return {'success': False, 'error': str(e)}

    def _generate_deployment_files(self, project_path: Path, config: ProjectConfig) -> Dict[str, Any]:
        """Generate deployment files."""
        try:
            generated_files = []

            # Generate Dockerfile if Docker support is enabled
            if config.docker_support:
                dockerfile_content = self._get_dockerfile_content(config)
                dockerfile = project_path / 'Dockerfile'
                with open(dockerfile, 'w') as f:
                    f.write(dockerfile_content)
                generated_files.append('Dockerfile')

                # Generate docker-compose.yml
                docker_compose_content = self._get_docker_compose_content(config)
                docker_compose = project_path / 'docker-compose.yml'
                with open(docker_compose, 'w') as f:
                    f.write(docker_compose_content)
                generated_files.append('docker-compose.yml')

            return {'success': True, 'generated_files': generated_files}

        except Exception as e:
            return {'success': False, 'error': str(e)}

    def _get_dockerfile_content(self, config: ProjectConfig) -> str:
        """Get Dockerfile content based on project type."""
        language = self._get_project_language(config.project_type)

        if language == 'python':
            return f'''FROM python:3.11-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 5000

CMD ["python", "run.py"]
'''
        elif language == 'javascript':
            return f'''FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .

EXPOSE 3000

CMD ["npm", "start"]
'''
        else:
            return f'''# Dockerfile for {config.project_type.value}
# Add appropriate Dockerfile content for this project type
'''

    def _get_docker_compose_content(self, config: ProjectConfig) -> str:
        """Get docker-compose.yml content."""
        return f'''version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    volumes:
      - .:/app
      - /app/node_modules
    depends_on:
      - db

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB={config.name}
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

volumes:
  postgres_data:
'''

    def _setup_cicd(self, project_path: Path, config: ProjectConfig) -> Dict[str, Any]:
        """Set up CI/CD configuration."""
        try:
            # Create GitHub Actions workflow
            github_dir = project_path / '.github' / 'workflows'
            github_dir.mkdir(parents=True, exist_ok=True)

            workflow_content = self._get_github_workflow_content(config)
            workflow_file = github_dir / 'ci.yml'
            with open(workflow_file, 'w') as f:
                f.write(workflow_content)

            return {'success': True, 'cicd_platform': 'github_actions', 'workflow_file': '.github/workflows/ci.yml'}

        except Exception as e:
            return {'success': False, 'error': str(e)}

    def _get_github_workflow_content(self, config: ProjectConfig) -> str:
        """Get GitHub Actions workflow content."""
        language = self._get_project_language(config.project_type)

        if language == 'python':
            return f'''name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.9, 3.10, 3.11]

    steps:
    - uses: actions/checkout@v3

    - name: Set up Python ${{{{ matrix.python-version }}}}
      uses: actions/setup-python@v4
      with:
        python-version: ${{{{ matrix.python-version }}}}

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-dev.txt

    - name: Run tests
      run: |
        pytest --cov=app --cov-report=xml

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v3

    - name: Deploy to production
      run: |
        echo "Deploy to production"
        # Add your deployment steps here
'''
        elif language == 'javascript':
            return f'''name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [16.x, 18.x, 20.x]

    steps:
    - uses: actions/checkout@v3

    - name: Use Node.js ${{{{ matrix.node-version }}}}
      uses: actions/setup-node@v3
      with:
        node-version: ${{{{ matrix.node-version }}}}
        cache: 'npm'

    - run: npm ci
    - run: npm run build --if-present
    - run: npm test

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v3

    - name: Deploy to production
      run: |
        echo "Deploy to production"
        # Add your deployment steps here
'''
        else:
            return f'''name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: Build project
      run: |
        echo "Build {config.project_type.value} project"
        # Add build steps for this project type
'''

    def _initialize_git(self, project_path: Path) -> Dict[str, Any]:
        """Initialize Git repository."""
        try:
            subprocess.run(['git', 'init'], check=True, cwd=project_path)
            subprocess.run(['git', 'add', '.'], check=True, cwd=project_path)
            subprocess.run(['git', 'commit', '-m', 'Initial commit'], check=True, cwd=project_path)

            return {'success': True, 'message': 'Git repository initialized'}

        except subprocess.CalledProcessError as e:
            return {'success': False, 'error': f'Failed to initialize Git: {e}'}
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def _get_next_steps(self, config: ProjectConfig) -> List[str]:
        """Get next steps for the user."""
        language = self._get_project_language(config.project_type)

        steps = [
            f"Navigate to the project directory: cd {config.name}",
            "Review the generated files and customize as needed"
        ]

        if language == 'python':
            steps.extend([
                "Activate virtual environment: source venv/bin/activate (Linux/Mac) or venv\\Scripts\\activate (Windows)",
                "Run the application: python run.py",
                "Run tests: pytest"
            ])
        elif language == 'javascript':
            steps.extend([
                "Install dependencies: npm install",
                "Start development server: npm start",
                "Run tests: npm test"
            ])

        steps.extend([
            "Set up environment variables in .env file",
            "Configure database connection if needed",
            "Push to Git repository",
            "Set up deployment pipeline"
        ])

        return steps

    def _get_react_package_json(self) -> str:
        """Get React package.json template."""
        return '''{
  "name": "{{PROJECT_NAME}}",
  "version": "0.1.0",
  "private": true,
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-scripts": "5.0.1",
    "web-vitals": "^2.1.4"
  },
  "scripts": {
    "start": "react-scripts start",
    "build": "react-scripts build",
    "test": "react-scripts test",
    "eject": "react-scripts eject"
  },
  "eslintConfig": {
    "extends": [
      "react-app",
      "react-app/jest"
    ]
  },
  "browserslist": {
    "production": [
      ">0.2%",
      "not dead",
      "not op_mini all"
    ],
    "development": [
      "last 1 chrome version",
      "last 1 firefox version",
      "last 1 safari version"
    ]
  }
}'''

    def _get_react_index(self) -> str:
        """Get React index.js template."""
        return '''import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App';

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);'''

    def _get_react_app(self) -> str:
        """Get React App.js template."""
        return '''import React from 'react';
import './App.css';

function App() {
  return (
    <div className="App">
      <header className="App-header">
        <h1>Welcome to {{PROJECT_NAME}}</h1>
        <p>{{PROJECT_DESCRIPTION}}</p>
        <p>
          Edit <code>src/App.js</code> and save to reload.
        </p>
      </header>
    </div>
  );
}

export default App;'''

    def _get_react_app_css(self) -> str:
        """Get React App.css template."""
        return '''.App {
  text-align: center;
}

.App-header {
  background-color: #282c34;
  padding: 20px;
  color: white;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
}

.App-header h1 {
  margin-bottom: 20px;
}

.App-header p {
  margin: 10px 0;
}

code {
  background-color: #f1f1f1;
  padding: 2px 4px;
  border-radius: 3px;
  color: #333;
}'''

    def _get_react_header_component(self) -> str:
        """Get React Header component template."""
        return '''import React from 'react';

const Header = ({ title }) => {
  return (
    <header className="header">
      <h1>{title}</h1>
      <nav>
        <ul>
          <li><a href="/">Home</a></li>
          <li><a href="/about">About</a></li>
          <li><a href="/contact">Contact</a></li>
        </ul>
      </nav>
    </header>
  );
};

export default Header;'''

    def _get_react_home_page(self) -> str:
        """Get React Home page template."""
        return '''import React from 'react';
import Header from '../components/Header';

const Home = () => {
  return (
    <div className="home">
      <Header title="{{PROJECT_NAME}}" />
      <main>
        <section className="hero">
          <h2>Welcome to {{PROJECT_NAME}}</h2>
          <p>{{PROJECT_DESCRIPTION}}</p>
        </section>

        <section className="features">
          <h3>Features</h3>
          <ul>
            <li>Modern React application</li>
            <li>Responsive design</li>
            <li>Component-based architecture</li>
            <li>Easy to customize</li>
          </ul>
        </section>
      </main>
    </div>
  );
};

export default Home;'''

    def _get_react_html(self) -> str:
        """Get React HTML template."""
        return '''<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="{{PROJECT_DESCRIPTION}}"
    />
    <title>{{PROJECT_NAME}}</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
  </body>
</html>'''

    def _get_react_manifest(self) -> str:
        """Get React manifest.json template."""
        return '''{
  "short_name": "{{PROJECT_NAME}}",
  "name": "{{PROJECT_NAME}} - {{PROJECT_DESCRIPTION}}",
  "icons": [
    {
      "src": "favicon.ico",
      "sizes": "64x64 32x32 24x24 16x16",
      "type": "image/x-icon"
    }
  ],
  "start_url": ".",
  "display": "standalone",
  "theme_color": "#000000",
  "background_color": "#ffffff"
}'''

    def _get_fastapi_main(self) -> str:
        """Get FastAPI main.py template."""
        return '''from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.api.v1.endpoints import router as api_router
from app.core.config import settings

app = FastAPI(
    title="{{PROJECT_NAME}}",
    description="{{PROJECT_DESCRIPTION}}",
    version="{{VERSION}}"
)

# Set up CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API router
app.include_router(api_router, prefix="/api/v1")

@app.get("/")
async def root():
    return {"message": "Welcome to {{PROJECT_NAME}}"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "{{PROJECT_NAME}}"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)'''

    def _get_fastapi_endpoints(self) -> str:
        """Get FastAPI endpoints template."""
        return '''from fastapi import APIRouter, HTTPException
from typing import List
from app.schemas import UserCreate, UserResponse

router = APIRouter()

@router.get("/users", response_model=List[UserResponse])
async def get_users():
    """Get all users."""
    # TODO: Implement user retrieval
    return []

@router.post("/users", response_model=UserResponse)
async def create_user(user: UserCreate):
    """Create a new user."""
    # TODO: Implement user creation
    return UserResponse(id=1, **user.dict())

@router.get("/users/{user_id}", response_model=UserResponse)
async def get_user(user_id: int):
    """Get a specific user."""
    # TODO: Implement user retrieval by ID
    if user_id == 1:
        return UserResponse(id=1, name="John Doe", email="<EMAIL>")
    raise HTTPException(status_code=404, detail="User not found")'''

    def _get_fastapi_config(self) -> str:
        """Get FastAPI config template."""
        return '''from pydantic import BaseSettings

class Settings(BaseSettings):
    app_name: str = "{{PROJECT_NAME}}"
    debug: bool = True
    database_url: str = "sqlite:///./app.db"
    secret_key: str = "your-secret-key-here"

    class Config:
        env_file = ".env"

settings = Settings()'''

    def _get_fastapi_security(self) -> str:
        """Get FastAPI security template."""
        return '''from datetime import datetime, timedelta
from typing import Optional
from jose import JWTError, jwt
from passlib.context import CryptContext
from fastapi import HTTPException, status

SECRET_KEY = "your-secret-key"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def verify_password(plain_password, hashed_password):
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password):
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt'''

    def _get_fastapi_requirements(self) -> str:
        """Get FastAPI requirements template."""
        return '''fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
sqlalchemy==2.0.23
alembic==1.13.0'''

    def _get_fastapi_tests(self) -> str:
        """Get FastAPI tests template."""
        return '''from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)

def test_read_main():
    response = client.get("/")
    assert response.status_code == 200
    assert response.json() == {"message": "Welcome to {{PROJECT_NAME}}"}

def test_health_check():
    response = client.get("/health")
    assert response.status_code == 200
    assert response.json()["status"] == "healthy"

def test_get_users():
    response = client.get("/api/v1/users")
    assert response.status_code == 200
    assert isinstance(response.json(), list)'''

    def _get_fastapi_dockerfile(self) -> str:
        """Get FastAPI Dockerfile template."""
        return '''FROM python:3.11-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 8000

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]'''

    def _get_fastapi_env(self) -> str:
        """Get FastAPI .env template."""
        return '''APP_NAME={{PROJECT_NAME}}
DEBUG=True
DATABASE_URL=sqlite:///./app.db
SECRET_KEY=your-secret-key-change-this-in-production'''

    def _get_nodejs_package_json(self) -> str:
        """Get Node.js package.json template."""
        return '''{
  "name": "{{PROJECT_NAME}}",
  "version": "{{VERSION}}",
  "description": "{{PROJECT_DESCRIPTION}}",
  "main": "src/server.js",
  "scripts": {
    "start": "node src/server.js",
    "dev": "nodemon src/server.js",
    "test": "jest",
    "test:watch": "jest --watch",
    "lint": "eslint src/",
    "lint:fix": "eslint src/ --fix"
  },
  "dependencies": {
    "express": "^4.18.2",
    "cors": "^2.8.5",
    "helmet": "^7.1.0",
    "morgan": "^1.10.0",
    "dotenv": "^16.3.1"
  },
  "devDependencies": {
    "nodemon": "^3.0.1",
    "jest": "^29.7.0",
    "supertest": "^6.3.3",
    "eslint": "^8.54.0"
  },
  "keywords": ["nodejs", "express", "api"],
  "author": "{{AUTHOR}}",
  "license": "{{LICENSE}}"
}'''

    def _get_nodejs_app(self) -> str:
        """Get Node.js app.js template."""
        return '''const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
require('dotenv').config();

const userRoutes = require('./routes/userRoutes');

const app = express();

// Middleware
app.use(helmet());
app.use(cors());
app.use(morgan('combined'));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Routes
app.use('/api/users', userRoutes);

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    service: '{{PROJECT_NAME}}',
    timestamp: new Date().toISOString()
  });
});

// Root route
app.get('/', (req, res) => {
  res.json({
    message: 'Welcome to {{PROJECT_NAME}}',
    version: '{{VERSION}}'
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Something went wrong!' });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Route not found' });
});

module.exports = app;'''

    def _get_nodejs_server(self) -> str:
        """Get Node.js server.js template."""
        return '''const app = require('./app');

const PORT = process.env.PORT || 3000;

app.listen(PORT, () => {
  console.log(`{{PROJECT_NAME}} server running on port ${PORT}`);
  console.log(`Health check: http://localhost:${PORT}/health`);
});'''

    def _get_nodejs_controller(self) -> str:
        """Get Node.js controller template."""
        return '''const users = []; // In-memory storage for demo

const getAllUsers = (req, res) => {
  res.json(users);
};

const getUserById = (req, res) => {
  const { id } = req.params;
  const user = users.find(u => u.id === parseInt(id));

  if (!user) {
    return res.status(404).json({ error: 'User not found' });
  }

  res.json(user);
};

const createUser = (req, res) => {
  const { name, email } = req.body;

  if (!name || !email) {
    return res.status(400).json({ error: 'Name and email are required' });
  }

  const newUser = {
    id: users.length + 1,
    name,
    email,
    createdAt: new Date().toISOString()
  };

  users.push(newUser);
  res.status(201).json(newUser);
};

const updateUser = (req, res) => {
  const { id } = req.params;
  const { name, email } = req.body;

  const userIndex = users.findIndex(u => u.id === parseInt(id));

  if (userIndex === -1) {
    return res.status(404).json({ error: 'User not found' });
  }

  users[userIndex] = { ...users[userIndex], name, email };
  res.json(users[userIndex]);
};

const deleteUser = (req, res) => {
  const { id } = req.params;
  const userIndex = users.findIndex(u => u.id === parseInt(id));

  if (userIndex === -1) {
    return res.status(404).json({ error: 'User not found' });
  }

  users.splice(userIndex, 1);
  res.status(204).send();
};

module.exports = {
  getAllUsers,
  getUserById,
  createUser,
  updateUser,
  deleteUser
};'''

    def _get_nodejs_routes(self) -> str:
        """Get Node.js routes template."""
        return '''const express = require('express');
const router = express.Router();
const userController = require('../controllers/userController');

// GET /api/users
router.get('/', userController.getAllUsers);

// GET /api/users/:id
router.get('/:id', userController.getUserById);

// POST /api/users
router.post('/', userController.createUser);

// PUT /api/users/:id
router.put('/:id', userController.updateUser);

// DELETE /api/users/:id
router.delete('/:id', userController.deleteUser);

module.exports = router;'''

    def _get_nodejs_middleware(self) -> str:
        """Get Node.js middleware template."""
        return '''const jwt = require('jsonwebtoken');

const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret', (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'Invalid token' });
    }
    req.user = user;
    next();
  });
};

const validateUser = (req, res, next) => {
  const { name, email } = req.body;

  if (!name || !email) {
    return res.status(400).json({ error: 'Name and email are required' });
  }

  // Basic email validation
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return res.status(400).json({ error: 'Invalid email format' });
  }

  next();
};

module.exports = {
  authenticateToken,
  validateUser
};'''

    def _get_nodejs_database(self) -> str:
        """Get Node.js database utility template."""
        return '''const fs = require('fs');
const path = require('path');

class Database {
  constructor() {
    this.dataFile = path.join(__dirname, '../../data/users.json');
    this.ensureDataFile();
  }

  ensureDataFile() {
    const dataDir = path.dirname(this.dataFile);
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }

    if (!fs.existsSync(this.dataFile)) {
      fs.writeFileSync(this.dataFile, JSON.stringify([]));
    }
  }

  readData() {
    try {
      const data = fs.readFileSync(this.dataFile, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      console.error('Error reading data:', error);
      return [];
    }
  }

  writeData(data) {
    try {
      fs.writeFileSync(this.dataFile, JSON.stringify(data, null, 2));
      return true;
    } catch (error) {
      console.error('Error writing data:', error);
      return false;
    }
  }

  findAll() {
    return this.readData();
  }

  findById(id) {
    const data = this.readData();
    return data.find(item => item.id === parseInt(id));
  }

  create(item) {
    const data = this.readData();
    const newItem = {
      id: data.length > 0 ? Math.max(...data.map(d => d.id)) + 1 : 1,
      ...item,
      createdAt: new Date().toISOString()
    };
    data.push(newItem);
    this.writeData(data);
    return newItem;
  }

  update(id, updates) {
    const data = this.readData();
    const index = data.findIndex(item => item.id === parseInt(id));

    if (index === -1) return null;

    data[index] = { ...data[index], ...updates, updatedAt: new Date().toISOString() };
    this.writeData(data);
    return data[index];
  }

  delete(id) {
    const data = this.readData();
    const index = data.findIndex(item => item.id === parseInt(id));

    if (index === -1) return false;

    data.splice(index, 1);
    this.writeData(data);
    return true;
  }
}

module.exports = new Database();'''

    def _get_nodejs_tests(self) -> str:
        """Get Node.js tests template."""
        return '''const request = require('supertest');
const app = require('../src/app');

describe('{{PROJECT_NAME}} API', () => {
  describe('GET /', () => {
    it('should return welcome message', async () => {
      const response = await request(app)
        .get('/')
        .expect(200);

      expect(response.body.message).toBe('Welcome to {{PROJECT_NAME}}');
    });
  });

  describe('GET /health', () => {
    it('should return health status', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200);

      expect(response.body.status).toBe('healthy');
    });
  });

  describe('Users API', () => {
    describe('GET /api/users', () => {
      it('should return empty array initially', async () => {
        const response = await request(app)
          .get('/api/users')
          .expect(200);

        expect(Array.isArray(response.body)).toBe(true);
      });
    });

    describe('POST /api/users', () => {
      it('should create a new user', async () => {
        const userData = {
          name: 'John Doe',
          email: '<EMAIL>'
        };

        const response = await request(app)
          .post('/api/users')
          .send(userData)
          .expect(201);

        expect(response.body.name).toBe(userData.name);
        expect(response.body.email).toBe(userData.email);
        expect(response.body.id).toBeDefined();
      });

      it('should return 400 for missing required fields', async () => {
        const response = await request(app)
          .post('/api/users')
          .send({})
          .expect(400);

        expect(response.body.error).toBeDefined();
      });
    });
  });
});'''

    def _get_nodejs_dockerfile(self) -> str:
        """Get Node.js Dockerfile template."""
        return '''FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY . .

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001

# Change ownership of the app directory
RUN chown -R nodejs:nodejs /app
USER nodejs

EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node healthcheck.js

CMD ["npm", "start"]'''

    def _get_nodejs_env(self) -> str:
        """Get Node.js .env template."""
        return '''# Application
NODE_ENV=development
PORT=3000
APP_NAME={{PROJECT_NAME}}

# Database
DATABASE_URL=sqlite:///./data/app.db

# Security
JWT_SECRET=your-jwt-secret-change-this-in-production
BCRYPT_ROUNDS=12

# Logging
LOG_LEVEL=info

# CORS
CORS_ORIGIN=http://localhost:3000'''

    def _add_authentication_files(self, structure: ProjectStructure, config: ProjectConfig):
        """Add authentication-related files to project structure."""
        if config.project_type in [ProjectType.FASTAPI_API, ProjectType.FLASK_API]:
            # Add Python authentication files
            structure.files['app/auth.py'] = '''
from datetime import datetime, timedelta
from typing import Optional
from jose import JWTError, jwt
from passlib.context import CryptContext

SECRET_KEY = "your-secret-key"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def verify_password(plain_password, hashed_password):
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password):
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt
'''
        elif config.project_type in [ProjectType.NODEJS_API, ProjectType.JAVASCRIPT_API]:
            # Add Node.js authentication files
            structure.files['src/auth/jwt.js'] = '''
const jwt = require('jsonwebtoken');
const bcrypt = require('bcrypt');

const JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '24h';

const generateToken = (payload) => {
  return jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });
};

const verifyToken = (token) => {
  return jwt.verify(token, JWT_SECRET);
};

const hashPassword = async (password) => {
  const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
  return await bcrypt.hash(password, saltRounds);
};

const comparePassword = async (password, hash) => {
  return await bcrypt.compare(password, hash);
};

module.exports = {
  generateToken,
  verifyToken,
  hashPassword,
  comparePassword
};
'''

    def _add_database_files(self, structure: ProjectStructure, config: ProjectConfig):
        """Add database-related files to project structure."""
        if config.database == 'postgresql':
            if config.project_type in [ProjectType.FASTAPI_API, ProjectType.FLASK_API]:
                structure.files['app/database.py'] = '''
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import os

DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://user:password@localhost/dbname")

engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
'''

    def _add_api_documentation(self, structure: ProjectStructure, config: ProjectConfig):
        """Add API documentation files."""
        if config.project_type in [ProjectType.FASTAPI_API, ProjectType.FLASK_API, ProjectType.NODEJS_API]:
            structure.files['docs/api.md'] = f'''
# {config.name} API Documentation

## Overview
{config.description}

## Authentication
This API uses JWT tokens for authentication.

## Endpoints

### Health Check
- **GET** `/health`
- Returns the health status of the API

### Users
- **GET** `/api/users` - Get all users
- **POST** `/api/users` - Create a new user
- **GET** `/api/users/{{id}}` - Get user by ID
- **PUT** `/api/users/{{id}}` - Update user
- **DELETE** `/api/users/{{id}}` - Delete user

## Error Responses
All endpoints return appropriate HTTP status codes and error messages in JSON format.
'''
