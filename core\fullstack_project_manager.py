"""
Full-Stack Project Manager
Comprehensive project management with multi-language support, dependency management,
testing framework integration, and deployment automation.
"""

import os
import json
import subprocess
import time
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
import shutil
import tempfile

from utils import get_logger

logger = get_logger()

class ProjectType(Enum):
    """Supported project types."""
    PYTHON_WEB = "python_web"
    PYTHON_API = "python_api"
    PYTHON_CLI = "python_cli"
    JAVASCRIPT_WEB = "javascript_web"
    JAVASCRIPT_API = "javascript_api"
    REACT_APP = "react_app"
    VUE_APP = "vue_app"
    ANGULAR_APP = "angular_app"
    NODEJS_API = "nodejs_api"
    FLASK_API = "flask_api"
    FASTAPI_API = "fastapi_api"
    DJANGO_WEB = "django_web"
    RUST_CLI = "rust_cli"
    RUST_WEB = "rust_web"
    GO_API = "go_api"
    GO_CLI = "go_cli"
    JAVA_SPRING = "java_spring"
    CSHARP_WEB = "csharp_web"
    MOBILE_REACT_NATIVE = "mobile_react_native"
    MOBILE_FLUTTER = "mobile_flutter"

class DeploymentTarget(Enum):
    """Deployment targets."""
    LOCAL = "local"
    DOCKER = "docker"
    KUBERNETES = "kubernetes"
    HEROKU = "heroku"
    VERCEL = "vercel"
    NETLIFY = "netlify"
    AWS = "aws"
    GCP = "gcp"
    AZURE = "azure"

@dataclass
class ProjectConfig:
    """Project configuration."""
    name: str
    project_type: ProjectType
    description: str = ""
    author: str = ""
    version: str = "1.0.0"
    license: str = "MIT"
    dependencies: List[str] = field(default_factory=list)
    dev_dependencies: List[str] = field(default_factory=list)
    features: List[str] = field(default_factory=list)
    deployment_targets: List[DeploymentTarget] = field(default_factory=list)
    testing_framework: str = "default"
    ci_cd: bool = True
    docker_support: bool = True
    database: Optional[str] = None
    authentication: bool = False
    api_documentation: bool = True

@dataclass
class ProjectStructure:
    """Project structure definition."""
    directories: List[str] = field(default_factory=list)
    files: Dict[str, str] = field(default_factory=dict)  # path -> content
    templates: Dict[str, str] = field(default_factory=dict)  # template_name -> content

class FullStackProjectManager:
    """Comprehensive full-stack project manager."""
    
    def __init__(self, workspace_dir: Path):
        self.workspace_dir = workspace_dir
        self.templates_dir = workspace_dir / ".templates"
        self.templates_dir.mkdir(exist_ok=True)
        
        # Initialize project templates
        self.project_templates = self._initialize_templates()
        
        # Dependency managers
        self.dependency_managers = {
            'python': self._manage_python_dependencies,
            'javascript': self._manage_javascript_dependencies,
            'rust': self._manage_rust_dependencies,
            'go': self._manage_go_dependencies,
            'java': self._manage_java_dependencies,
            'csharp': self._manage_csharp_dependencies
        }
    
    def create_fullstack_project(self, config: ProjectConfig) -> Dict[str, Any]:
        """Create a complete full-stack project."""
        try:
            project_path = self.workspace_dir / config.name
            if project_path.exists():
                return {
                    'success': False,
                    'error': f'Project {config.name} already exists'
                }
            
            # Create project directory
            project_path.mkdir(parents=True)
            
            # Generate project structure
            structure = self._generate_project_structure(config)
            
            # Create directories
            for directory in structure.directories:
                (project_path / directory).mkdir(parents=True, exist_ok=True)
            
            # Create files
            created_files = []
            for file_path, content in structure.files.items():
                full_path = project_path / file_path
                full_path.parent.mkdir(parents=True, exist_ok=True)
                with open(full_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                created_files.append(str(full_path))
            
            # Install dependencies
            dependency_result = self._install_dependencies(project_path, config)
            
            # Set up testing framework
            testing_result = self._setup_testing(project_path, config)
            
            # Generate deployment files
            deployment_result = self._generate_deployment_files(project_path, config)
            
            # Set up CI/CD
            cicd_result = self._setup_cicd(project_path, config) if config.ci_cd else {'success': True}
            
            # Initialize Git repository
            git_result = self._initialize_git(project_path)
            
            return {
                'success': True,
                'project_name': config.name,
                'project_path': str(project_path),
                'project_type': config.project_type.value,
                'created_files': created_files,
                'dependencies': dependency_result,
                'testing': testing_result,
                'deployment': deployment_result,
                'cicd': cicd_result,
                'git': git_result,
                'next_steps': self._get_next_steps(config)
            }
            
        except Exception as e:
            logger.error(f"Error creating full-stack project: {e}")
            return {
                'success': False,
                'error': str(e),
                'project_name': config.name
            }
    
    def _initialize_templates(self) -> Dict[ProjectType, ProjectStructure]:
        """Initialize project templates for different types."""
        templates = {}
        
        # Python Web Application
        templates[ProjectType.PYTHON_WEB] = ProjectStructure(
            directories=[
                'app', 'app/templates', 'app/static', 'app/static/css', 
                'app/static/js', 'tests', 'docs', 'scripts'
            ],
            files={
                'app/__init__.py': self._get_python_web_init(),
                'app/main.py': self._get_python_web_main(),
                'app/models.py': self._get_python_models(),
                'app/routes.py': self._get_python_routes(),
                'app/templates/base.html': self._get_base_html_template(),
                'app/templates/index.html': self._get_index_html_template(),
                'app/static/css/style.css': self._get_base_css(),
                'requirements.txt': 'flask==2.3.3\nwerkzeug==2.3.7\nflask-sqlalchemy==3.0.5\nflask-migrate==4.0.5',
                'config.py': self._get_python_config(),
                'run.py': self._get_python_run_script(),
                'tests/test_app.py': self._get_python_tests(),
                'README.md': self._get_readme_template('Python Web Application'),
                '.env.example': self._get_env_template(),
                '.gitignore': self._get_python_gitignore()
            }
        )
        
        # React Application
        templates[ProjectType.REACT_APP] = ProjectStructure(
            directories=[
                'src', 'src/components', 'src/pages', 'src/hooks', 
                'src/utils', 'src/styles', 'public', 'tests'
            ],
            files={
                'package.json': self._get_react_package_json(),
                'src/index.js': self._get_react_index(),
                'src/App.js': self._get_react_app(),
                'src/App.css': self._get_react_app_css(),
                'src/components/Header.js': self._get_react_header_component(),
                'src/pages/Home.js': self._get_react_home_page(),
                'public/index.html': self._get_react_html(),
                'public/manifest.json': self._get_react_manifest(),
                'README.md': self._get_readme_template('React Application'),
                '.gitignore': self._get_javascript_gitignore(),
                '.env.example': 'REACT_APP_API_URL=http://localhost:3001'
            }
        )
        
        # FastAPI Application
        templates[ProjectType.FASTAPI_API] = ProjectStructure(
            directories=[
                'app', 'app/api', 'app/api/v1', 'app/core', 'app/models', 
                'app/schemas', 'app/crud', 'tests', 'alembic'
            ],
            files={
                'app/__init__.py': '',
                'app/main.py': self._get_fastapi_main(),
                'app/api/__init__.py': '',
                'app/api/v1/__init__.py': '',
                'app/api/v1/endpoints.py': self._get_fastapi_endpoints(),
                'app/core/config.py': self._get_fastapi_config(),
                'app/core/security.py': self._get_fastapi_security(),
                'app/models/__init__.py': '',
                'app/schemas/__init__.py': '',
                'app/crud/__init__.py': '',
                'requirements.txt': self._get_fastapi_requirements(),
                'tests/test_main.py': self._get_fastapi_tests(),
                'README.md': self._get_readme_template('FastAPI Application'),
                'Dockerfile': self._get_fastapi_dockerfile(),
                '.env.example': self._get_fastapi_env(),
                '.gitignore': self._get_python_gitignore()
            }
        )
        
        # Node.js API
        templates[ProjectType.NODEJS_API] = ProjectStructure(
            directories=[
                'src', 'src/controllers', 'src/models', 'src/routes', 
                'src/middleware', 'src/utils', 'tests', 'docs'
            ],
            files={
                'package.json': self._get_nodejs_package_json(),
                'src/app.js': self._get_nodejs_app(),
                'src/server.js': self._get_nodejs_server(),
                'src/controllers/userController.js': self._get_nodejs_controller(),
                'src/routes/userRoutes.js': self._get_nodejs_routes(),
                'src/middleware/auth.js': self._get_nodejs_middleware(),
                'src/utils/database.js': self._get_nodejs_database(),
                'tests/user.test.js': self._get_nodejs_tests(),
                'README.md': self._get_readme_template('Node.js API'),
                'Dockerfile': self._get_nodejs_dockerfile(),
                '.env.example': self._get_nodejs_env(),
                '.gitignore': self._get_javascript_gitignore()
            }
        )
        
        return templates
    
    def _generate_project_structure(self, config: ProjectConfig) -> ProjectStructure:
        """Generate project structure based on configuration."""
        base_structure = self.project_templates.get(config.project_type)
        if not base_structure:
            raise ValueError(f"Unsupported project type: {config.project_type}")
        
        # Clone the base structure
        structure = ProjectStructure(
            directories=base_structure.directories.copy(),
            files=base_structure.files.copy(),
            templates=base_structure.templates.copy()
        )
        
        # Customize based on features
        if 'authentication' in config.features or config.authentication:
            self._add_authentication_files(structure, config)
        
        if 'database' in config.features or config.database:
            self._add_database_files(structure, config)
        
        if 'api_documentation' in config.features or config.api_documentation:
            self._add_api_documentation(structure, config)
        
        # Replace placeholders in file contents
        for file_path, content in structure.files.items():
            structure.files[file_path] = self._replace_placeholders(content, config)
        
        return structure
    
    def _replace_placeholders(self, content: str, config: ProjectConfig) -> str:
        """Replace placeholders in file content."""
        replacements = {
            '{{PROJECT_NAME}}': config.name,
            '{{PROJECT_DESCRIPTION}}': config.description,
            '{{AUTHOR}}': config.author,
            '{{VERSION}}': config.version,
            '{{LICENSE}}': config.license
        }
        
        for placeholder, value in replacements.items():
            content = content.replace(placeholder, value)
        
        return content

    def _get_python_web_init(self) -> str:
        """Get Python web application __init__.py."""
        return '''from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate

db = SQLAlchemy()
migrate = Migrate()

def create_app():
    app = Flask(__name__)
    app.config.from_object('config.Config')

    db.init_app(app)
    migrate.init_app(app, db)

    from app.routes import main
    app.register_blueprint(main)

    return app
'''

    def _get_python_web_main(self) -> str:
        """Get Python web application main.py."""
        return '''from app import create_app

app = create_app()

if __name__ == '__main__':
    app.run(debug=True)
'''

    def _get_python_models(self) -> str:
        """Get Python models template."""
        return '''from app import db
from datetime import datetime

class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<User {self.username}>'
'''

    def _get_python_routes(self) -> str:
        """Get Python routes template."""
        return '''from flask import Blueprint, render_template, request, jsonify
from app.models import User
from app import db

main = Blueprint('main', __name__)

@main.route('/')
def index():
    return render_template('index.html', title='{{PROJECT_NAME}}')

@main.route('/api/users', methods=['GET'])
def get_users():
    users = User.query.all()
    return jsonify([{'id': u.id, 'username': u.username, 'email': u.email} for u in users])

@main.route('/api/users', methods=['POST'])
def create_user():
    data = request.get_json()
    user = User(username=data['username'], email=data['email'])
    db.session.add(user)
    db.session.commit()
    return jsonify({'id': user.id, 'username': user.username, 'email': user.email}), 201
'''

    def _get_base_html_template(self) -> str:
        """Get base HTML template."""
        return '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{PROJECT_NAME}}{% endblock %}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <header>
        <nav>
            <h1>{{PROJECT_NAME}}</h1>
        </nav>
    </header>

    <main>
        {% block content %}{% endblock %}
    </main>

    <footer>
        <p>&copy; 2024 {{PROJECT_NAME}}. All rights reserved.</p>
    </footer>

    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
</body>
</html>
'''

    def _get_index_html_template(self) -> str:
        """Get index HTML template."""
        return '''{% extends "base.html" %}

{% block content %}
<div class="container">
    <h2>Welcome to {{PROJECT_NAME}}</h2>
    <p>{{PROJECT_DESCRIPTION}}</p>

    <div class="features">
        <h3>Features</h3>
        <ul>
            <li>Modern web application</li>
            <li>RESTful API</li>
            <li>Database integration</li>
            <li>Responsive design</li>
        </ul>
    </div>
</div>
{% endblock %}
'''

    def _get_base_css(self) -> str:
        """Get base CSS template."""
        return '''/* Base styles for {{PROJECT_NAME}} */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f4f4f4;
}

header {
    background: #333;
    color: white;
    padding: 1rem 0;
}

nav h1 {
    text-align: center;
}

main {
    min-height: calc(100vh - 120px);
    padding: 2rem 0;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

footer {
    background: #333;
    color: white;
    text-align: center;
    padding: 1rem 0;
}

.features ul {
    list-style-type: none;
    padding: 1rem 0;
}

.features li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #ddd;
}
'''

    def _get_python_config(self) -> str:
        """Get Python configuration template."""
        return '''import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key'
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///app.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
'''

    def _get_python_run_script(self) -> str:
        """Get Python run script."""
        return '''#!/usr/bin/env python3
from app import create_app

app = create_app()

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)
'''

    def _get_python_tests(self) -> str:
        """Get Python test template."""
        return '''import unittest
from app import create_app, db
from app.models import User

class TestApp(unittest.TestCase):
    def setUp(self):
        self.app = create_app()
        self.app.config['TESTING'] = True
        self.app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
        self.client = self.app.test_client()

        with self.app.app_context():
            db.create_all()

    def tearDown(self):
        with self.app.app_context():
            db.session.remove()
            db.drop_all()

    def test_index_page(self):
        response = self.client.get('/')
        self.assertEqual(response.status_code, 200)

    def test_create_user(self):
        response = self.client.post('/api/users',
                                  json={'username': 'testuser', 'email': '<EMAIL>'})
        self.assertEqual(response.status_code, 201)

if __name__ == '__main__':
    unittest.main()
'''

    def _get_readme_template(self, project_type: str) -> str:
        """Get README template."""
        return f'''# {{{{PROJECT_NAME}}}}

{{{{PROJECT_DESCRIPTION}}}}

## {project_type}

### Features

- Modern architecture
- RESTful API
- Database integration
- Testing framework
- CI/CD ready
- Docker support

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd {{{{PROJECT_NAME}}}}

# Install dependencies
# (See specific instructions below based on technology)

# Run the application
# (See specific instructions below)
```

### Development

```bash
# Run in development mode
# (Technology-specific commands)

# Run tests
# (Technology-specific commands)

# Build for production
# (Technology-specific commands)
```

### Deployment

This project includes Docker support and CI/CD configuration for easy deployment.

### License

{{{{LICENSE}}}}
'''

    def _get_env_template(self) -> str:
        """Get environment template."""
        return '''# Environment variables
SECRET_KEY=your-secret-key-here
DATABASE_URL=sqlite:///app.db
DEBUG=True
'''

    def _get_python_gitignore(self) -> str:
        """Get Python .gitignore."""
        return '''# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo

# Environment variables
.env
.env.local

# Database
*.db
*.sqlite3

# Logs
*.log

# OS
.DS_Store
Thumbs.db
'''

    def _get_javascript_gitignore(self) -> str:
        """Get JavaScript .gitignore."""
        return '''# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Production build
build/
dist/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/

# OS
.DS_Store
Thumbs.db

# Logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
'''

    def _install_dependencies(self, project_path: Path, config: ProjectConfig) -> Dict[str, Any]:
        """Install project dependencies."""
        try:
            language = self._get_project_language(config.project_type)
            if language in self.dependency_managers:
                return self.dependency_managers[language](project_path, config)
            else:
                return {'success': True, 'message': 'No dependency manager for this project type'}
        except Exception as e:
            logger.error(f"Error installing dependencies: {e}")
            return {'success': False, 'error': str(e)}

    def _get_project_language(self, project_type: ProjectType) -> str:
        """Get the primary language for a project type."""
        language_map = {
            ProjectType.PYTHON_WEB: 'python',
            ProjectType.PYTHON_API: 'python',
            ProjectType.PYTHON_CLI: 'python',
            ProjectType.FLASK_API: 'python',
            ProjectType.FASTAPI_API: 'python',
            ProjectType.DJANGO_WEB: 'python',
            ProjectType.JAVASCRIPT_WEB: 'javascript',
            ProjectType.JAVASCRIPT_API: 'javascript',
            ProjectType.REACT_APP: 'javascript',
            ProjectType.VUE_APP: 'javascript',
            ProjectType.ANGULAR_APP: 'javascript',
            ProjectType.NODEJS_API: 'javascript',
            ProjectType.MOBILE_REACT_NATIVE: 'javascript',
            ProjectType.RUST_CLI: 'rust',
            ProjectType.RUST_WEB: 'rust',
            ProjectType.GO_API: 'go',
            ProjectType.GO_CLI: 'go',
            ProjectType.JAVA_SPRING: 'java',
            ProjectType.CSHARP_WEB: 'csharp',
            ProjectType.MOBILE_FLUTTER: 'dart'
        }
        return language_map.get(project_type, 'unknown')

    def _manage_python_dependencies(self, project_path: Path, config: ProjectConfig) -> Dict[str, Any]:
        """Manage Python dependencies."""
        try:
            # Create virtual environment
            venv_path = project_path / 'venv'
            subprocess.run(['python', '-m', 'venv', str(venv_path)], check=True, cwd=project_path)

            # Determine pip executable
            if os.name == 'nt':  # Windows
                pip_exe = venv_path / 'Scripts' / 'pip.exe'
            else:  # Unix-like
                pip_exe = venv_path / 'bin' / 'pip'

            # Install requirements
            requirements_file = project_path / 'requirements.txt'
            if requirements_file.exists():
                subprocess.run([str(pip_exe), 'install', '-r', 'requirements.txt'],
                             check=True, cwd=project_path)

            # Install additional dependencies
            if config.dependencies:
                subprocess.run([str(pip_exe), 'install'] + config.dependencies,
                             check=True, cwd=project_path)

            return {
                'success': True,
                'virtual_env': str(venv_path),
                'installed_packages': config.dependencies
            }

        except subprocess.CalledProcessError as e:
            return {'success': False, 'error': f'Failed to install Python dependencies: {e}'}
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def _manage_javascript_dependencies(self, project_path: Path, config: ProjectConfig) -> Dict[str, Any]:
        """Manage JavaScript dependencies."""
        try:
            # Check if package.json exists
            package_json = project_path / 'package.json'
            if not package_json.exists():
                return {'success': False, 'error': 'package.json not found'}

            # Install dependencies using npm
            subprocess.run(['npm', 'install'], check=True, cwd=project_path)

            # Install additional dependencies
            if config.dependencies:
                subprocess.run(['npm', 'install'] + config.dependencies,
                             check=True, cwd=project_path)

            # Install dev dependencies
            if config.dev_dependencies:
                subprocess.run(['npm', 'install', '--save-dev'] + config.dev_dependencies,
                             check=True, cwd=project_path)

            return {
                'success': True,
                'package_manager': 'npm',
                'installed_packages': config.dependencies,
                'dev_packages': config.dev_dependencies
            }

        except subprocess.CalledProcessError as e:
            return {'success': False, 'error': f'Failed to install JavaScript dependencies: {e}'}
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def _manage_rust_dependencies(self, project_path: Path, config: ProjectConfig) -> Dict[str, Any]:
        """Manage Rust dependencies."""
        try:
            # Check if Cargo.toml exists
            cargo_toml = project_path / 'Cargo.toml'
            if not cargo_toml.exists():
                return {'success': False, 'error': 'Cargo.toml not found'}

            # Build the project (this will download dependencies)
            subprocess.run(['cargo', 'build'], check=True, cwd=project_path)

            return {'success': True, 'package_manager': 'cargo'}

        except subprocess.CalledProcessError as e:
            return {'success': False, 'error': f'Failed to build Rust project: {e}'}
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def _manage_go_dependencies(self, project_path: Path, config: ProjectConfig) -> Dict[str, Any]:
        """Manage Go dependencies."""
        try:
            # Check if go.mod exists
            go_mod = project_path / 'go.mod'
            if not go_mod.exists():
                return {'success': False, 'error': 'go.mod not found'}

            # Download dependencies
            subprocess.run(['go', 'mod', 'download'], check=True, cwd=project_path)
            subprocess.run(['go', 'mod', 'tidy'], check=True, cwd=project_path)

            return {'success': True, 'package_manager': 'go mod'}

        except subprocess.CalledProcessError as e:
            return {'success': False, 'error': f'Failed to manage Go dependencies: {e}'}
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def _manage_java_dependencies(self, project_path: Path, config: ProjectConfig) -> Dict[str, Any]:
        """Manage Java dependencies."""
        try:
            # Check for Maven or Gradle
            pom_xml = project_path / 'pom.xml'
            build_gradle = project_path / 'build.gradle'

            if pom_xml.exists():
                subprocess.run(['mvn', 'dependency:resolve'], check=True, cwd=project_path)
                return {'success': True, 'package_manager': 'maven'}
            elif build_gradle.exists():
                subprocess.run(['gradle', 'build'], check=True, cwd=project_path)
                return {'success': True, 'package_manager': 'gradle'}
            else:
                return {'success': False, 'error': 'No Maven or Gradle configuration found'}

        except subprocess.CalledProcessError as e:
            return {'success': False, 'error': f'Failed to manage Java dependencies: {e}'}
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def _manage_csharp_dependencies(self, project_path: Path, config: ProjectConfig) -> Dict[str, Any]:
        """Manage C# dependencies."""
        try:
            # Check for .csproj files
            csproj_files = list(project_path.glob('*.csproj'))
            if not csproj_files:
                return {'success': False, 'error': 'No .csproj file found'}

            # Restore packages
            subprocess.run(['dotnet', 'restore'], check=True, cwd=project_path)

            return {'success': True, 'package_manager': 'dotnet'}

        except subprocess.CalledProcessError as e:
            return {'success': False, 'error': f'Failed to restore .NET packages: {e}'}
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def _setup_testing(self, project_path: Path, config: ProjectConfig) -> Dict[str, Any]:
        """Set up testing framework."""
        try:
            language = self._get_project_language(config.project_type)

            if language == 'python':
                return self._setup_python_testing(project_path, config)
            elif language == 'javascript':
                return self._setup_javascript_testing(project_path, config)
            else:
                return {'success': True, 'message': 'Testing setup not implemented for this language'}

        except Exception as e:
            return {'success': False, 'error': str(e)}

    def _setup_python_testing(self, project_path: Path, config: ProjectConfig) -> Dict[str, Any]:
        """Set up Python testing."""
        try:
            # Create pytest configuration
            pytest_ini = project_path / 'pytest.ini'
            with open(pytest_ini, 'w') as f:
                f.write('''[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = -v --tb=short
''')

            # Add pytest to requirements
            requirements_file = project_path / 'requirements-dev.txt'
            with open(requirements_file, 'w') as f:
                f.write('pytest==7.4.3\npytest-cov==4.1.0\npytest-mock==3.12.0\n')

            return {'success': True, 'framework': 'pytest', 'config_file': 'pytest.ini'}

        except Exception as e:
            return {'success': False, 'error': str(e)}

    def _setup_javascript_testing(self, project_path: Path, config: ProjectConfig) -> Dict[str, Any]:
        """Set up JavaScript testing."""
        try:
            # Create Jest configuration
            jest_config = project_path / 'jest.config.js'
            with open(jest_config, 'w') as f:
                f.write('''module.exports = {
  testEnvironment: 'node',
  collectCoverageFrom: [
    'src/**/*.{js,jsx}',
    '!src/index.js'
  ],
  coverageDirectory: 'coverage',
  testMatch: [
    '<rootDir>/tests/**/*.test.{js,jsx}',
    '<rootDir>/src/**/*.test.{js,jsx}'
  ]
};
''')

            return {'success': True, 'framework': 'jest', 'config_file': 'jest.config.js'}

        except Exception as e:
            return {'success': False, 'error': str(e)}

    def _generate_deployment_files(self, project_path: Path, config: ProjectConfig) -> Dict[str, Any]:
        """Generate deployment files."""
        try:
            generated_files = []

            # Generate Dockerfile if Docker support is enabled
            if config.docker_support:
                dockerfile_content = self._get_dockerfile_content(config)
                dockerfile = project_path / 'Dockerfile'
                with open(dockerfile, 'w') as f:
                    f.write(dockerfile_content)
                generated_files.append('Dockerfile')

                # Generate docker-compose.yml
                docker_compose_content = self._get_docker_compose_content(config)
                docker_compose = project_path / 'docker-compose.yml'
                with open(docker_compose, 'w') as f:
                    f.write(docker_compose_content)
                generated_files.append('docker-compose.yml')

            return {'success': True, 'generated_files': generated_files}

        except Exception as e:
            return {'success': False, 'error': str(e)}

    def _get_dockerfile_content(self, config: ProjectConfig) -> str:
        """Get Dockerfile content based on project type."""
        language = self._get_project_language(config.project_type)

        if language == 'python':
            return f'''FROM python:3.11-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 5000

CMD ["python", "run.py"]
'''
        elif language == 'javascript':
            return f'''FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .

EXPOSE 3000

CMD ["npm", "start"]
'''
        else:
            return f'''# Dockerfile for {config.project_type.value}
# Add appropriate Dockerfile content for this project type
'''

    def _get_docker_compose_content(self, config: ProjectConfig) -> str:
        """Get docker-compose.yml content."""
        return f'''version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    volumes:
      - .:/app
      - /app/node_modules
    depends_on:
      - db

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB={config.name}
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

volumes:
  postgres_data:
'''

    def _setup_cicd(self, project_path: Path, config: ProjectConfig) -> Dict[str, Any]:
        """Set up CI/CD configuration."""
        try:
            # Create GitHub Actions workflow
            github_dir = project_path / '.github' / 'workflows'
            github_dir.mkdir(parents=True, exist_ok=True)

            workflow_content = self._get_github_workflow_content(config)
            workflow_file = github_dir / 'ci.yml'
            with open(workflow_file, 'w') as f:
                f.write(workflow_content)

            return {'success': True, 'cicd_platform': 'github_actions', 'workflow_file': '.github/workflows/ci.yml'}

        except Exception as e:
            return {'success': False, 'error': str(e)}

    def _get_github_workflow_content(self, config: ProjectConfig) -> str:
        """Get GitHub Actions workflow content."""
        language = self._get_project_language(config.project_type)

        if language == 'python':
            return f'''name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.9, 3.10, 3.11]

    steps:
    - uses: actions/checkout@v3

    - name: Set up Python ${{{{ matrix.python-version }}}}
      uses: actions/setup-python@v4
      with:
        python-version: ${{{{ matrix.python-version }}}}

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-dev.txt

    - name: Run tests
      run: |
        pytest --cov=app --cov-report=xml

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v3

    - name: Deploy to production
      run: |
        echo "Deploy to production"
        # Add your deployment steps here
'''
        elif language == 'javascript':
            return f'''name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [16.x, 18.x, 20.x]

    steps:
    - uses: actions/checkout@v3

    - name: Use Node.js ${{{{ matrix.node-version }}}}
      uses: actions/setup-node@v3
      with:
        node-version: ${{{{ matrix.node-version }}}}
        cache: 'npm'

    - run: npm ci
    - run: npm run build --if-present
    - run: npm test

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v3

    - name: Deploy to production
      run: |
        echo "Deploy to production"
        # Add your deployment steps here
'''
        else:
            return f'''name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: Build project
      run: |
        echo "Build {config.project_type.value} project"
        # Add build steps for this project type
'''

    def _initialize_git(self, project_path: Path) -> Dict[str, Any]:
        """Initialize Git repository."""
        try:
            subprocess.run(['git', 'init'], check=True, cwd=project_path)
            subprocess.run(['git', 'add', '.'], check=True, cwd=project_path)
            subprocess.run(['git', 'commit', '-m', 'Initial commit'], check=True, cwd=project_path)

            return {'success': True, 'message': 'Git repository initialized'}

        except subprocess.CalledProcessError as e:
            return {'success': False, 'error': f'Failed to initialize Git: {e}'}
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def _get_next_steps(self, config: ProjectConfig) -> List[str]:
        """Get next steps for the user."""
        language = self._get_project_language(config.project_type)

        steps = [
            f"Navigate to the project directory: cd {config.name}",
            "Review the generated files and customize as needed"
        ]

        if language == 'python':
            steps.extend([
                "Activate virtual environment: source venv/bin/activate (Linux/Mac) or venv\\Scripts\\activate (Windows)",
                "Run the application: python run.py",
                "Run tests: pytest"
            ])
        elif language == 'javascript':
            steps.extend([
                "Install dependencies: npm install",
                "Start development server: npm start",
                "Run tests: npm test"
            ])

        steps.extend([
            "Set up environment variables in .env file",
            "Configure database connection if needed",
            "Push to Git repository",
            "Set up deployment pipeline"
        ])

        return steps
