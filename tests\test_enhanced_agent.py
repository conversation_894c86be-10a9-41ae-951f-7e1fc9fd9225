"""
Comprehensive tests for the Enhanced AI Agent
"""

import unittest
import tempfile
import shutil
import json
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

import sys
sys.path.append(str(Path(__file__).parent.parent))

from agent import EnhancedAgent, WorkflowStatus, TaskType
from models import ModelManager
from conversation import ConversationManager
from tests import TEST_CONFIG


class TestEnhancedAgent(unittest.TestCase):
    """Test suite for Enhanced AI Agent."""
    
    def setUp(self):
        """Set up test environment."""
        self.test_workspace = TEST_CONFIG['workspace_dir']
        self.test_workspace.mkdir(exist_ok=True)
        
        # Mock dependencies
        self.mock_model_manager = Mock(spec=ModelManager)
        self.mock_conversation_manager = Mock(spec=ConversationManager)
        
        # Create agent instance
        self.agent = EnhancedAgent(
            model_manager=self.mock_model_manager,
            conversation_manager=self.mock_conversation_manager,
            workspace_dir=self.test_workspace
        )
    
    def tearDown(self):
        """Clean up test environment."""
        if self.test_workspace.exists():
            shutil.rmtree(self.test_workspace, ignore_errors=True)
    
    def test_agent_initialization(self):
        """Test agent initialization."""
        self.assertIsNotNone(self.agent)
        self.assertEqual(self.agent.workspace_dir, self.test_workspace)
        self.assertIsNotNone(self.agent.tool_manager)
        self.assertIsNotNone(self.agent.workflow_manager)
        self.assertIsNotNone(self.agent.fullstack_manager)
        self.assertIsNotNone(self.agent.thread_manager)
    
    def test_tool_registration(self):
        """Test that all tools are properly registered."""
        tools = self.agent.tool_manager.tools
        
        # Check core tools
        essential_tools = [
            'shell_execute', 'file_read', 'file_write', 'file_delete',
            'code_execute', 'web_search', 'codebase_analyze',
            'advanced_grep', 'create_project', 'generate_dockerfile'
        ]
        
        for tool_name in essential_tools:
            self.assertIn(tool_name, tools, f"Tool '{tool_name}' not registered")
    
    def test_file_operations(self):
        """Test file operations."""
        test_file = self.test_workspace / 'test.txt'
        test_content = 'Hello, World!'
        
        # Test file write
        result = self.agent._file_write(str(test_file), test_content)
        self.assertIn('successfully', result.lower())
        self.assertTrue(test_file.exists())
        
        # Test file read
        result = self.agent._file_read(str(test_file))
        self.assertIn(test_content, result)
        
        # Test file delete
        result = self.agent._file_delete(str(test_file))
        self.assertIn('successfully', result.lower())
        self.assertFalse(test_file.exists())
    
    def test_advanced_grep(self):
        """Test advanced grep functionality."""
        # Create test files
        test_dir = self.test_workspace / 'grep_test'
        test_dir.mkdir(exist_ok=True)
        
        (test_dir / 'file1.py').write_text('def hello():\n    print("Hello")\n    return True')
        (test_dir / 'file2.py').write_text('class World:\n    def __init__(self):\n        self.name = "World"')
        
        # Test grep
        result = self.agent._advanced_grep('def', str(test_dir / '*.py'))
        result_data = json.loads(result)
        
        self.assertTrue(result_data['success'])
        self.assertGreater(result_data['total_matches'], 0)
    
    def test_project_creation(self):
        """Test project creation functionality."""
        project_name = 'test_project'
        project_type = 'python'
        
        result = self.agent._create_project(project_name, project_type)
        result_data = json.loads(result)
        
        self.assertTrue(result_data['success'])
        self.assertEqual(result_data['project_name'], project_name)
        
        # Check if project directory was created
        project_path = self.test_workspace / project_name
        self.assertTrue(project_path.exists())
    
    def test_workflow_management(self):
        """Test workflow management functionality."""
        user_input = "Create a Python web application"
        
        # Test workflow plan creation
        result = self.agent._create_workflow_plan(user_input)
        result_data = json.loads(result)
        
        self.assertTrue(result_data['success'])
        self.assertIn('plan_id', result_data)
        self.assertIn('steps', result_data)
        self.assertGreater(len(result_data['steps']), 0)
    
    def test_code_analysis(self):
        """Test code analysis functionality."""
        # Create a test Python file
        test_file = self.test_workspace / 'test_code.py'
        test_code = '''
def calculate_sum(a, b):
    """Calculate sum of two numbers."""
    return a + b

class Calculator:
    def __init__(self):
        self.history = []
    
    def add(self, x, y):
        result = x + y
        self.history.append(f"{x} + {y} = {result}")
        return result
'''
        test_file.write_text(test_code)
        
        # Test code complexity analysis
        result = self.agent._analyze_code_complexity(str(test_file))
        result_data = json.loads(result)
        
        self.assertTrue(result_data['success'])
        self.assertIn('analysis', result_data)
        
        analysis = result_data['analysis']
        self.assertGreater(analysis['total_lines'], 0)
        self.assertGreater(analysis['function_count'], 0)
        self.assertGreater(analysis['class_count'], 0)
    
    def test_fullstack_project_creation(self):
        """Test full-stack project creation."""
        project_name = 'fullstack_test'
        project_type = 'react_app'
        
        result = self.agent._create_fullstack_project(project_name, project_type)
        result_data = json.loads(result)
        
        self.assertTrue(result_data['success'])
        self.assertEqual(result_data['project_name'], project_name)
        self.assertIn('created_files', result_data)
    
    def test_terminal_session_management(self):
        """Test terminal session management."""
        session_id = 'test_session'
        
        # Create session
        result = self.agent._create_terminal_session(session_id)
        result_data = json.loads(result)
        self.assertTrue(result_data['success'])
        
        # Execute command in session
        result = self.agent._execute_in_session(session_id, 'echo "Hello"')
        result_data = json.loads(result)
        # Note: This might fail in test environment without proper shell
        
        # Close session
        result = self.agent._close_terminal_session(session_id)
        result_data = json.loads(result)
        self.assertTrue(result_data['success'])
    
    def test_error_handling(self):
        """Test error handling in various operations."""
        # Test file operation with invalid path
        result = self.agent._file_read('/nonexistent/path/file.txt')
        self.assertIn('Error', result)
        
        # Test project creation with invalid type
        result = self.agent._create_fullstack_project('test', 'invalid_type')
        self.assertIn('Error', result)
    
    def test_workflow_context_management(self):
        """Test workflow context management."""
        # Test workflow status tracking
        self.assertIsNotNone(self.agent.workflow_context)
        self.assertEqual(self.agent.workflow_context.completed_tasks, [])
        self.assertEqual(self.agent.workflow_context.pending_tasks, [])
        
        # Test workflow progress tracking
        status = self.agent.get_workflow_status()
        self.assertIn('conversation_id', status)
        self.assertIn('total_completed', status)
        self.assertIn('total_pending', status)
    
    def test_predictive_capabilities(self):
        """Test predictive capabilities."""
        user_input = "Build a web application"
        predictions = self.agent.predict_next_steps(user_input)
        
        self.assertIsInstance(predictions, list)
        self.assertGreater(len(predictions), 0)
    
    def test_system_prompt_generation(self):
        """Test system prompt generation."""
        prompt = self.agent._get_default_system_prompt()
        
        self.assertIsInstance(prompt, str)
        self.assertGreater(len(prompt), 100)
        self.assertIn('Enhanced AI Agent', prompt)
        self.assertIn('Multi-threaded Execution', prompt)
    
    def test_performance_monitoring(self):
        """Test performance monitoring capabilities."""
        # Test workflow progress tracking
        task_id = 'test_task'
        self.agent._update_workflow_progress(
            task_id, 'Test Task', TaskType.CODE_GENERATION, 50.0
        )
        
        # Check if task was tracked
        self.assertGreater(len(self.agent.workflow_context.pending_tasks), 0)
    
    def test_concurrent_operations(self):
        """Test concurrent operations handling."""
        import threading
        import time
        
        results = []
        
        def create_file(index):
            file_path = f'concurrent_test_{index}.txt'
            result = self.agent._file_write(file_path, f'Content {index}')
            results.append(result)
        
        # Create multiple threads
        threads = []
        for i in range(5):
            thread = threading.Thread(target=create_file, args=(i,))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Check results
        self.assertEqual(len(results), 5)
        for result in results:
            self.assertIn('successfully', result.lower())


class TestAgentIntegration(unittest.TestCase):
    """Integration tests for the Enhanced AI Agent."""
    
    def setUp(self):
        """Set up integration test environment."""
        self.test_workspace = TEST_CONFIG['workspace_dir'] / 'integration'
        self.test_workspace.mkdir(exist_ok=True)
        
        # Mock dependencies
        self.mock_model_manager = Mock(spec=ModelManager)
        self.mock_conversation_manager = Mock(spec=ConversationManager)
        
        # Create agent instance
        self.agent = EnhancedAgent(
            model_manager=self.mock_model_manager,
            conversation_manager=self.mock_conversation_manager,
            workspace_dir=self.test_workspace
        )
    
    def tearDown(self):
        """Clean up integration test environment."""
        if self.test_workspace.exists():
            shutil.rmtree(self.test_workspace, ignore_errors=True)
    
    def test_end_to_end_project_workflow(self):
        """Test complete project creation and management workflow."""
        # Step 1: Create a full-stack project
        project_name = 'e2e_test_project'
        result = self.agent._create_fullstack_project(project_name, 'python_web')
        result_data = json.loads(result)
        self.assertTrue(result_data['success'])
        
        # Step 2: Analyze the created project
        project_path = project_name
        result = self.agent._analyze_project_structure(project_path)
        result_data = json.loads(result)
        self.assertIn('structure_analysis', result_data)
        
        # Step 3: Generate tests for the project
        result = self.agent._generate_project_tests(project_path)
        result_data = json.loads(result)
        self.assertTrue(result_data['success'])
        
        # Step 4: Set up deployment pipeline
        result = self.agent._setup_deployment_pipeline(project_path)
        result_data = json.loads(result)
        self.assertIn('deployment', result_data)
    
    def test_workflow_with_error_recovery(self):
        """Test workflow execution with error recovery."""
        # Create a workflow that will encounter errors
        user_input = "Create a project in an invalid location"
        
        # Create workflow plan
        result = self.agent._create_workflow_plan(user_input)
        result_data = json.loads(result)
        self.assertTrue(result_data['success'])
        
        # The workflow should handle errors gracefully
        plan_id = result_data['plan_id']
        progress = self.agent._get_workflow_progress(plan_id)
        progress_data = json.loads(progress)
        
        self.assertIn('progress_percentage', progress_data)


if __name__ == '__main__':
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_suite.addTest(unittest.makeSuite(TestEnhancedAgent))
    test_suite.addTest(unittest.makeSuite(TestAgentIntegration))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print(f"\nTests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\nFailures:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\nErrors:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
