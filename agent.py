"""
Enhanced AI Agent with Multi-threaded Execution, Context-Aware Auto-Refactoring,
Terminal Command Execution, Predictive Prefetching, Parallel Task Handling,
Natural Language Processing, and Comprehensive File Operations.
"""

import re
import json
import time
import threading
import asyncio
import uuid
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Tuple, Generator, Callable
from dataclasses import dataclass, field
from enum import Enum
from concurrent.futures import ThreadPoolExecutor, Future

from models import ModelManager
from conversation import Conversation, ConversationManager
from tools import (
    ShellTool, FileTool, CodeTool, WebTool, CodebaseTool, VisionTool,
    PatchTool, BrowserTool, RagTool, SearchAPI,
    WebScraperTool, InformationSynthesizer, WebInfoManager
)
from tools.enhanced_file_tool import EnhancedFileTool
from core.intelligent_workflow_manager import IntelligentWorkflowManager
from core.fullstack_project_manager import FullStackProjectManager, ProjectConfig, ProjectType
from tools.threading.thread_manager import ThreadManager
from tools.predictive.prefetcher import PredictivePrefetcher
from tools.predictive.context_analyzer import ContextA<PERSON>yzer
from tools.predictive.code_predictor import CodePredictor
from tools.tool_manager import Tool, ToolManager
from core.ai_code_assistant import AICodeAssistant, AssistantRequest
from utils import get_logger

# Get the logger
logger = get_logger()

class WorkflowStatus(Enum):
    """Workflow execution status."""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    PAUSED = "paused"

class TaskType(Enum):
    """Types of tasks the agent can handle."""
    CODE_GENERATION = "code_generation"
    CODE_ANALYSIS = "code_analysis"
    FILE_OPERATION = "file_operation"
    TERMINAL_COMMAND = "terminal_command"
    WEB_SEARCH = "web_search"
    REFACTORING = "refactoring"
    TESTING = "testing"
    DEBUGGING = "debugging"
    PROJECT_SETUP = "project_setup"
    DEPLOYMENT = "deployment"

@dataclass
class TaskProgress:
    """Task progress tracking."""
    task_id: str
    name: str
    task_type: TaskType
    status: WorkflowStatus
    progress_percentage: float = 0.0
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    error_message: Optional[str] = None
    next_steps: List[str] = field(default_factory=list)
    context_data: Dict[str, Any] = field(default_factory=dict)

@dataclass
class WorkflowContext:
    """Comprehensive workflow context."""
    conversation_id: str
    user_intent: str
    current_task: Optional[TaskProgress] = None
    completed_tasks: List[TaskProgress] = field(default_factory=list)
    pending_tasks: List[TaskProgress] = field(default_factory=list)
    context_data: Dict[str, Any] = field(default_factory=dict)
    predictions: List[str] = field(default_factory=list)
    chat_history: List[Dict[str, Any]] = field(default_factory=list)

class EnhancedAgent:
    """Enhanced AI Agent with comprehensive capabilities."""

    def __init__(
        self,
        model_manager: ModelManager,
        conversation_manager: ConversationManager,
        workspace_dir: Optional[Path] = None,
        system_prompt: Optional[str] = None
    ):
        """Initialize the enhanced agent."""
        self.model_manager = model_manager
        self.conversation_manager = conversation_manager
        self.workspace_dir = workspace_dir or Path.cwd()
        self._has_rag = False
        
        # Threading and async capabilities
        self.thread_manager = ThreadManager(max_workers=8)
        self.executor = ThreadPoolExecutor(max_workers=4)
        self.lock = threading.RLock()
        
        # Workflow management
        self.workflow_context = WorkflowContext(
            conversation_id=str(uuid.uuid4()),
            user_intent=""
        )
        
        # Initialize tool manager
        self.tool_manager = ToolManager()
        
        # Initialize tools
        self._initialize_tools()
        
        # Initialize AI Code Assistant
        self.ai_code_assistant = AICodeAssistant(self.model_manager, self.workspace_dir)

        # Initialize Intelligent Workflow Manager
        self.workflow_manager = IntelligentWorkflowManager(self.workspace_dir)
        
        # Initialize predictive systems
        try:
            self.context_analyzer = ContextAnalyzer(self.workspace_dir)
            self.code_predictor = CodePredictor(self.model_manager, self.workspace_dir)
            self.prefetcher = PredictivePrefetcher(
                self.thread_manager, 
                self.context_analyzer, 
                self.code_predictor, 
                self.model_manager
            )
        except Exception as e:
            logger.warning(f"Could not initialize predictive systems: {e}")
            self.context_analyzer = None
            self.code_predictor = None
            self.prefetcher = None
        
        # Set the system prompt
        if system_prompt is None:
            self.system_prompt = self._get_default_system_prompt()
        else:
            self.system_prompt = system_prompt

    def _initialize_tools(self):
        """Initialize and register all tools."""
        # Initialize individual tool instances
        self.shell_tool = ShellTool(self.workspace_dir)
        self.file_tool = EnhancedFileTool(self.workspace_dir)  # Use enhanced file tool
        self.code_tool = CodeTool()
        self.web_tool = WebTool()
        self.codebase_tool = CodebaseTool(self.workspace_dir)
        self.vision_tool = VisionTool(self.workspace_dir)
        self.patch_tool = PatchTool(self.workspace_dir)
        self.browser_tool = BrowserTool()
        self.search_api = SearchAPI()
        self.web_scraper = WebScraperTool()
        self.info_synthesizer = InformationSynthesizer()
        self.web_info_manager = WebInfoManager()

        # Initialize RAG tool if dependencies are available
        try:
            import importlib.util
            has_faiss = importlib.util.find_spec("faiss") is not None
            has_sentence_transformers = importlib.util.find_spec("sentence_transformers") is not None

            if has_faiss and has_sentence_transformers:
                self.rag_tool = RagTool(self.workspace_dir)
                self._has_rag = True
            else:
                self._has_rag = False
        except Exception:
            self._has_rag = False

        # Register all tools
        self._register_all_tools()

    def _register_all_tools(self):
        """Register all tools with the ToolManager."""
        self._register_shell_tools()
        self._register_file_tools()
        self._register_code_tools()
        self._register_web_tools()
        self._register_codebase_tools()
        self._register_vision_tools()
        self._register_patch_tools()
        self._register_browser_tools()
        if self._has_rag:
            self._register_rag_tools()
        self._register_advanced_tools()

    def _register_shell_tools(self):
        """Register shell-related tools."""
        self.tool_manager.register_tool(Tool(
            name="shell_execute",
            description="Execute shell commands with enhanced error handling and output capture.",
            function=self._execute_shell_command,
            parameters={
                "type": "object",
                "properties": {
                    "command": {"type": "string", "description": "The shell command to execute."},
                    "timeout": {"type": "number", "description": "Timeout in seconds (default: 30)."},
                    "capture_output": {"type": "boolean", "description": "Whether to capture output (default: true)."}
                },
                "required": ["command"]
            }
        ))

    def _register_file_tools(self):
        """Register file-related tools."""
        file_tools = [
            ("file_read", "Read content from a file.", self._file_read, ["path"]),
            ("file_write", "Write content to a file.", self._file_write, ["path", "content"]),
            ("file_append", "Append content to a file.", self._file_append, ["path", "content"]),
            ("file_delete", "Delete a file or directory.", self._file_delete, ["path"]),
            ("file_list", "List files and directories.", self._file_list, ["path"]),
            ("file_search", "Search for files matching a pattern.", self._file_search, ["pattern", "path"]),
            ("file_grep", "Search for text patterns within files.", self._file_grep, ["pattern", "file_pattern"]),
            ("file_copy", "Copy a file or directory.", self._file_copy, ["source", "destination"]),
            ("file_move", "Move/rename a file or directory.", self._file_move, ["source", "destination"]),
            ("file_permissions", "Get or set file permissions.", self._file_permissions, ["path", "mode"])
        ]
        
        for name, desc, func, required_params in file_tools:
            properties = {}
            for param in required_params:
                if param in ["path", "source", "destination", "pattern", "file_pattern"]:
                    properties[param] = {"type": "string", "description": f"The {param} parameter."}
                elif param == "content":
                    properties[param] = {"type": "string", "description": "The content to write/append."}
                elif param == "mode":
                    properties[param] = {"type": "string", "description": "File permissions mode (e.g., '755')."}
            
            self.tool_manager.register_tool(Tool(
                name=name,
                description=desc,
                function=func,
                parameters={
                    "type": "object",
                    "properties": properties,
                    "required": required_params
                }
            ))

    def _execute_shell_command(self, command: str, timeout: float = 30.0, capture_output: bool = True) -> str:
        """Execute a shell command with enhanced error handling."""
        try:
            task_id = str(uuid.uuid4())
            self._update_workflow_progress(task_id, "Executing shell command", TaskType.TERMINAL_COMMAND, 0.0)

            stdout, stderr, return_code = self.shell_tool.execute(command, timeout=timeout)

            if return_code == 0:
                self._update_workflow_progress(task_id, "Shell command completed", TaskType.TERMINAL_COMMAND, 100.0)
                if stdout.strip():
                    return f"Command executed successfully:\n\n{stdout}"
                else:
                    return "Command executed successfully (no output)"
            else:
                error_msg = f"Command failed with return code {return_code}:\n\n{stderr}"
                self._update_workflow_progress(task_id, "Shell command failed", TaskType.TERMINAL_COMMAND, 100.0, error_msg)
                return error_msg

        except Exception as e:
            error_msg = f"Error executing command: {e}"
            self._update_workflow_progress(task_id, "Shell command error", TaskType.TERMINAL_COMMAND, 100.0, error_msg)
            return error_msg

    def _file_read(self, path: str) -> str:
        """Read content from a file."""
        try:
            task_id = str(uuid.uuid4())
            self._update_workflow_progress(task_id, f"Reading file: {path}", TaskType.FILE_OPERATION, 0.0)

            content = self.file_tool.read_file(path)
            self._update_workflow_progress(task_id, f"File read successfully: {path}", TaskType.FILE_OPERATION, 100.0)
            return f"File content from {path}:\n\n{content}"
        except Exception as e:
            error_msg = f"Error reading file {path}: {e}"
            self._update_workflow_progress(task_id, f"File read failed: {path}", TaskType.FILE_OPERATION, 100.0, error_msg)
            return error_msg

    def _file_write(self, path: str, content: str) -> str:
        """Write content to a file."""
        try:
            task_id = str(uuid.uuid4())
            self._update_workflow_progress(task_id, f"Writing file: {path}", TaskType.FILE_OPERATION, 0.0)

            # Create parent directories if they don't exist
            path_obj = Path(self.workspace_dir) / path
            path_obj.parent.mkdir(parents=True, exist_ok=True)

            self.file_tool.write_file(path, content)
            self._update_workflow_progress(task_id, f"File written successfully: {path}", TaskType.FILE_OPERATION, 100.0)
            return f"File written successfully: {path}"
        except Exception as e:
            error_msg = f"Error writing file {path}: {e}"
            self._update_workflow_progress(task_id, f"File write failed: {path}", TaskType.FILE_OPERATION, 100.0, error_msg)
            return error_msg

    def _file_append(self, path: str, content: str) -> str:
        """Append content to a file."""
        try:
            task_id = str(uuid.uuid4())
            self._update_workflow_progress(task_id, f"Appending to file: {path}", TaskType.FILE_OPERATION, 0.0)

            # Create parent directories if they don't exist
            path_obj = Path(self.workspace_dir) / path
            path_obj.parent.mkdir(parents=True, exist_ok=True)

            self.file_tool.append_file(path, content)
            self._update_workflow_progress(task_id, f"Content appended successfully: {path}", TaskType.FILE_OPERATION, 100.0)
            return f"Content appended successfully to: {path}"
        except Exception as e:
            error_msg = f"Error appending to file {path}: {e}"
            self._update_workflow_progress(task_id, f"File append failed: {path}", TaskType.FILE_OPERATION, 100.0, error_msg)
            return error_msg

    def _file_delete(self, path: str) -> str:
        """Delete a file or directory."""
        try:
            task_id = str(uuid.uuid4())
            self._update_workflow_progress(task_id, f"Deleting: {path}", TaskType.FILE_OPERATION, 0.0)

            self.file_tool.delete_file(path)
            self._update_workflow_progress(task_id, f"Deleted successfully: {path}", TaskType.FILE_OPERATION, 100.0)
            return f"Deleted successfully: {path}"
        except Exception as e:
            error_msg = f"Error deleting {path}: {e}"
            self._update_workflow_progress(task_id, f"Delete failed: {path}", TaskType.FILE_OPERATION, 100.0, error_msg)
            return error_msg

    def _file_list(self, path: str = ".") -> str:
        """List files and directories."""
        try:
            task_id = str(uuid.uuid4())
            self._update_workflow_progress(task_id, f"Listing directory: {path}", TaskType.FILE_OPERATION, 0.0)

            files = self.file_tool.list_files(path)
            self._update_workflow_progress(task_id, f"Directory listed: {path}", TaskType.FILE_OPERATION, 100.0)
            return f"Files in {path}:\n\n{json.dumps(files, indent=2)}"
        except Exception as e:
            error_msg = f"Error listing directory {path}: {e}"
            self._update_workflow_progress(task_id, f"Directory listing failed: {path}", TaskType.FILE_OPERATION, 100.0, error_msg)
            return error_msg

    def _file_search(self, pattern: str, path: str = ".") -> str:
        """Search for files matching a pattern."""
        try:
            task_id = str(uuid.uuid4())
            self._update_workflow_progress(task_id, f"Searching files: {pattern}", TaskType.FILE_OPERATION, 0.0)

            files = self.file_tool.search_files(pattern, path)
            self._update_workflow_progress(task_id, f"File search completed: {pattern}", TaskType.FILE_OPERATION, 100.0)
            return f"Files matching {pattern}:\n\n{json.dumps(files, indent=2)}"
        except Exception as e:
            error_msg = f"Error searching files with pattern {pattern}: {e}"
            self._update_workflow_progress(task_id, f"File search failed: {pattern}", TaskType.FILE_OPERATION, 100.0, error_msg)
            return error_msg

    def _file_grep(self, pattern: str, file_pattern: str = "*") -> str:
        """Search for text patterns within files."""
        try:
            task_id = str(uuid.uuid4())
            self._update_workflow_progress(task_id, f"Grepping pattern: {pattern}", TaskType.FILE_OPERATION, 0.0)

            results = self.file_tool.grep_files(pattern, file_pattern)
            self._update_workflow_progress(task_id, f"Grep completed: {pattern}", TaskType.FILE_OPERATION, 100.0)
            return f"Grep results for {pattern} in {file_pattern}:\n\n{json.dumps(results, indent=2)}"
        except Exception as e:
            error_msg = f"Error grepping pattern {pattern}: {e}"
            self._update_workflow_progress(task_id, f"Grep failed: {pattern}", TaskType.FILE_OPERATION, 100.0, error_msg)
            return error_msg

    def _file_copy(self, source: str, destination: str) -> str:
        """Copy a file or directory."""
        try:
            task_id = str(uuid.uuid4())
            self._update_workflow_progress(task_id, f"Copying: {source} -> {destination}", TaskType.FILE_OPERATION, 0.0)

            self.file_tool.copy_file(source, destination)
            self._update_workflow_progress(task_id, f"Copied successfully: {source} -> {destination}", TaskType.FILE_OPERATION, 100.0)
            return f"Copied successfully: {source} -> {destination}"
        except Exception as e:
            error_msg = f"Error copying {source} to {destination}: {e}"
            self._update_workflow_progress(task_id, f"Copy failed: {source} -> {destination}", TaskType.FILE_OPERATION, 100.0, error_msg)
            return error_msg

    def _file_move(self, source: str, destination: str) -> str:
        """Move/rename a file or directory."""
        try:
            task_id = str(uuid.uuid4())
            self._update_workflow_progress(task_id, f"Moving: {source} -> {destination}", TaskType.FILE_OPERATION, 0.0)

            self.file_tool.move_file(source, destination)
            self._update_workflow_progress(task_id, f"Moved successfully: {source} -> {destination}", TaskType.FILE_OPERATION, 100.0)
            return f"Moved successfully: {source} -> {destination}"
        except Exception as e:
            error_msg = f"Error moving {source} to {destination}: {e}"
            self._update_workflow_progress(task_id, f"Move failed: {source} -> {destination}", TaskType.FILE_OPERATION, 100.0, error_msg)
            return error_msg

    def _file_permissions(self, path: str, mode: Optional[str] = None) -> str:
        """Get or set file permissions."""
        try:
            task_id = str(uuid.uuid4())
            if mode:
                self._update_workflow_progress(task_id, f"Setting permissions: {path} -> {mode}", TaskType.FILE_OPERATION, 0.0)
                self.file_tool.set_permissions(path, mode)
                self._update_workflow_progress(task_id, f"Permissions set: {path} -> {mode}", TaskType.FILE_OPERATION, 100.0)
                return f"Permissions set successfully: {path} -> {mode}"
            else:
                self._update_workflow_progress(task_id, f"Getting permissions: {path}", TaskType.FILE_OPERATION, 0.0)
                perms = self.file_tool.get_permissions(path)
                self._update_workflow_progress(task_id, f"Permissions retrieved: {path}", TaskType.FILE_OPERATION, 100.0)
                return f"Permissions for {path}: {perms}"
        except Exception as e:
            error_msg = f"Error with permissions for {path}: {e}"
            self._update_workflow_progress(task_id, f"Permissions operation failed: {path}", TaskType.FILE_OPERATION, 100.0, error_msg)
            return error_msg

    def _register_code_tools(self):
        """Register code-related tools."""
        self.tool_manager.register_tool(Tool(
            name="code_execute",
            description="Execute code in various programming languages with enhanced error handling.",
            function=self._execute_code,
            parameters={
                "type": "object",
                "properties": {
                    "language": {"type": "string", "description": "Programming language (python, javascript, etc.)"},
                    "code": {"type": "string", "description": "Code to execute"},
                    "timeout": {"type": "number", "description": "Timeout in seconds (default: 30)"}
                },
                "required": ["language", "code"]
            }
        ))

        self.tool_manager.register_tool(Tool(
            name="code_analyze",
            description="Analyze code for structure, dependencies, and potential issues.",
            function=self._analyze_code,
            parameters={
                "type": "object",
                "properties": {
                    "code": {"type": "string", "description": "Code to analyze"},
                    "language": {"type": "string", "description": "Programming language"}
                },
                "required": ["code"]
            }
        ))

    def _register_web_tools(self):
        """Register web-related tools."""
        web_tools = [
            ("web_search", "Search the web for information.", self._web_search, ["query"]),
            ("web_fetch", "Fetch content from a URL.", self._web_fetch, ["url"]),
            ("web_scrape", "Scrape structured data from a webpage.", self._web_scrape, ["url", "selector"]),
            ("web_download", "Download a file from a URL.", self._web_download, ["url", "path"])
        ]

        for name, desc, func, required_params in web_tools:
            properties = {}
            for param in required_params:
                if param == "query":
                    properties[param] = {"type": "string", "description": "Search query"}
                elif param == "url":
                    properties[param] = {"type": "string", "description": "URL to process"}
                elif param == "selector":
                    properties[param] = {"type": "string", "description": "CSS selector for scraping"}
                elif param == "path":
                    properties[param] = {"type": "string", "description": "Local path to save file"}

            self.tool_manager.register_tool(Tool(
                name=name,
                description=desc,
                function=func,
                parameters={
                    "type": "object",
                    "properties": properties,
                    "required": required_params
                }
            ))

    def _register_codebase_tools(self):
        """Register codebase analysis tools."""
        codebase_tools = [
            ("codebase_find_files", "Find files in codebase matching pattern.", self._codebase_find_files, ["pattern"]),
            ("codebase_search", "Search for patterns in code files.", self._codebase_search, ["pattern"]),
            ("codebase_analyze", "Analyze code file structure.", self._codebase_analyze, ["path"]),
            ("codebase_dependencies", "Analyze project dependencies.", self._codebase_dependencies, []),
            ("codebase_metrics", "Get codebase metrics and statistics.", self._codebase_metrics, [])
        ]

        for name, desc, func, required_params in codebase_tools:
            properties = {}
            for param in required_params:
                if param == "pattern":
                    properties[param] = {"type": "string", "description": "Search pattern or file pattern"}
                elif param == "path":
                    properties[param] = {"type": "string", "description": "File path to analyze"}

            self.tool_manager.register_tool(Tool(
                name=name,
                description=desc,
                function=func,
                parameters={
                    "type": "object",
                    "properties": properties,
                    "required": required_params
                }
            ))

    def _register_vision_tools(self):
        """Register vision-related tools."""
        self.tool_manager.register_tool(Tool(
            name="vision_screenshot",
            description="Take a screenshot of the current screen.",
            function=self._take_screenshot,
            parameters={
                "type": "object",
                "properties": {
                    "path": {"type": "string", "description": "Optional path to save screenshot"}
                },
                "required": []
            }
        ))

        self.tool_manager.register_tool(Tool(
            name="vision_analyze_image",
            description="Analyze an image file.",
            function=self._analyze_image,
            parameters={
                "type": "object",
                "properties": {
                    "path": {"type": "string", "description": "Path to image file"}
                },
                "required": ["path"]
            }
        ))

    def _register_patch_tools(self):
        """Register patch-related tools."""
        self.tool_manager.register_tool(Tool(
            name="patch_apply",
            description="Apply a code patch to a file.",
            function=self._apply_patch,
            parameters={
                "type": "object",
                "properties": {
                    "file_path": {"type": "string", "description": "Path to file to patch"},
                    "original_code": {"type": "string", "description": "Original code to replace"},
                    "updated_code": {"type": "string", "description": "New code to insert"}
                },
                "required": ["file_path", "original_code", "updated_code"]
            }
        ))

    def _register_browser_tools(self):
        """Register browser automation tools."""
        self.tool_manager.register_tool(Tool(
            name="browser_open",
            description="Open a URL in browser and interact with it.",
            function=self._browser_open,
            parameters={
                "type": "object",
                "properties": {
                    "url": {"type": "string", "description": "URL to open"},
                    "action": {"type": "string", "description": "Action to perform (optional)"}
                },
                "required": ["url"]
            }
        ))

    def _register_rag_tools(self):
        """Register RAG (Retrieval-Augmented Generation) tools."""
        if self._has_rag:
            self.tool_manager.register_tool(Tool(
                name="rag_query",
                description="Query the RAG system for relevant information.",
                function=self._rag_query,
                parameters={
                    "type": "object",
                    "properties": {
                        "query": {"type": "string", "description": "Query to search for"},
                        "context": {"type": "string", "description": "Additional context (optional)"}
                    },
                    "required": ["query"]
                }
            ))

    def _update_workflow_progress(self, task_id: str, name: str, task_type: TaskType,
                                progress: float, error_message: Optional[str] = None):
        """Update workflow progress tracking."""
        with self.lock:
            current_time = time.time()

            # Find existing task or create new one
            task = None
            for t in self.workflow_context.pending_tasks:
                if t.task_id == task_id:
                    task = t
                    break

            if task is None:
                task = TaskProgress(
                    task_id=task_id,
                    name=name,
                    task_type=task_type,
                    status=WorkflowStatus.NOT_STARTED,
                    start_time=current_time
                )
                self.workflow_context.pending_tasks.append(task)

            # Update task progress
            task.progress_percentage = progress
            task.error_message = error_message

            if progress >= 100.0:
                task.status = WorkflowStatus.FAILED if error_message else WorkflowStatus.COMPLETED
                task.end_time = current_time

                # Move to completed tasks
                if task in self.workflow_context.pending_tasks:
                    self.workflow_context.pending_tasks.remove(task)
                self.workflow_context.completed_tasks.append(task)

                # Predict next steps
                if self.prefetcher and not error_message:
                    try:
                        next_steps = self.prefetcher.predict_next_actions(
                            task.name, task.task_type.value, self.workflow_context.context_data
                        )
                        task.next_steps = next_steps
                        self.workflow_context.predictions.extend(next_steps)
                    except Exception as e:
                        logger.warning(f"Could not predict next steps: {e}")
            else:
                task.status = WorkflowStatus.IN_PROGRESS

            self.workflow_context.current_task = task

    def _execute_code(self, language: str, code: str, timeout: float = 30.0) -> str:
        """Execute code with enhanced error handling."""
        try:
            task_id = str(uuid.uuid4())
            self._update_workflow_progress(task_id, f"Executing {language} code", TaskType.CODE_GENERATION, 0.0)

            stdout, stderr, return_code = self.code_tool.execute(code, language, timeout=timeout)

            if return_code == 0:
                self._update_workflow_progress(task_id, f"{language} code executed successfully", TaskType.CODE_GENERATION, 100.0)
                if stdout.strip():
                    return f"{language.capitalize()} execution output:\n\n{stdout}"
                else:
                    return f"{language.capitalize()} code executed successfully with no output."
            else:
                error_msg = f"{language.capitalize()} execution error (return code {return_code}):\n\n{stderr}"
                self._update_workflow_progress(task_id, f"{language} code execution failed", TaskType.CODE_GENERATION, 100.0, error_msg)
                return error_msg

        except Exception as e:
            error_msg = f"Error executing {language} code: {e}"
            self._update_workflow_progress(task_id, f"{language} code execution error", TaskType.CODE_GENERATION, 100.0, error_msg)
            return error_msg

    def _analyze_code(self, code: str, language: Optional[str] = None) -> str:
        """Analyze code structure and quality."""
        try:
            task_id = str(uuid.uuid4())
            self._update_workflow_progress(task_id, "Analyzing code", TaskType.CODE_ANALYSIS, 0.0)

            # Use AI assistant for code analysis
            analysis_request = AssistantRequest(
                task_type="code_analysis",
                content=code,
                context={"language": language} if language else {}
            )

            analysis_result = self.ai_code_assistant.process_request(analysis_request)
            self._update_workflow_progress(task_id, "Code analysis completed", TaskType.CODE_ANALYSIS, 100.0)

            return f"Code analysis results:\n\n{analysis_result.content}"

        except Exception as e:
            error_msg = f"Error analyzing code: {e}"
            self._update_workflow_progress(task_id, "Code analysis failed", TaskType.CODE_ANALYSIS, 100.0, error_msg)
            return error_msg

    def _web_search(self, query: str) -> str:
        """Search the web for information."""
        try:
            task_id = str(uuid.uuid4())
            self._update_workflow_progress(task_id, f"Web search: {query}", TaskType.WEB_SEARCH, 0.0)

            results = self.web_tool.search(query)
            self._update_workflow_progress(task_id, f"Web search completed: {query}", TaskType.WEB_SEARCH, 100.0)

            if results:
                return f"Web search results for '{query}':\n\n{json.dumps(results, indent=2)}"
            else:
                return f"No results found for query: {query}"

        except Exception as e:
            error_msg = f"Error performing web search: {e}"
            self._update_workflow_progress(task_id, f"Web search failed: {query}", TaskType.WEB_SEARCH, 100.0, error_msg)
            return error_msg

    def _web_fetch(self, url: str) -> str:
        """Fetch content from a URL."""
        try:
            task_id = str(uuid.uuid4())
            self._update_workflow_progress(task_id, f"Fetching URL: {url}", TaskType.WEB_SEARCH, 0.0)

            content, metadata = self.web_tool.fetch_url(url)
            self._update_workflow_progress(task_id, f"URL fetched: {url}", TaskType.WEB_SEARCH, 100.0)

            return (
                f"Content from {url}:\n"
                f"Title: {metadata.get('title', 'N/A')}\n"
                f"Content length: {len(content)} characters\n\n"
                f"{content[:2000]}..."  # Return first 2000 chars
            )

        except Exception as e:
            error_msg = f"Error fetching URL {url}: {e}"
            self._update_workflow_progress(task_id, f"URL fetch failed: {url}", TaskType.WEB_SEARCH, 100.0, error_msg)
            return error_msg

    def _web_scrape(self, url: str, selector: str) -> str:
        """Scrape structured data from a webpage."""
        try:
            task_id = str(uuid.uuid4())
            self._update_workflow_progress(task_id, f"Scraping URL: {url}", TaskType.WEB_SEARCH, 0.0)

            data = self.web_scraper.scrape(url, selector)
            self._update_workflow_progress(task_id, f"URL scraped: {url}", TaskType.WEB_SEARCH, 100.0)

            return f"Scraped data from {url} using selector '{selector}':\n\n{json.dumps(data, indent=2)}"

        except Exception as e:
            error_msg = f"Error scraping URL {url}: {e}"
            self._update_workflow_progress(task_id, f"URL scraping failed: {url}", TaskType.WEB_SEARCH, 100.0, error_msg)
            return error_msg

    def _web_download(self, url: str, path: str) -> str:
        """Download a file from a URL."""
        try:
            task_id = str(uuid.uuid4())
            self._update_workflow_progress(task_id, f"Downloading: {url}", TaskType.WEB_SEARCH, 0.0)

            # Create parent directories if they don't exist
            path_obj = Path(self.workspace_dir) / path
            path_obj.parent.mkdir(parents=True, exist_ok=True)

            self.web_tool.download_file(url, str(path_obj))
            self._update_workflow_progress(task_id, f"Downloaded: {url} -> {path}", TaskType.WEB_SEARCH, 100.0)

            return f"File downloaded successfully: {url} -> {path}"

        except Exception as e:
            error_msg = f"Error downloading file from {url}: {e}"
            self._update_workflow_progress(task_id, f"Download failed: {url}", TaskType.WEB_SEARCH, 100.0, error_msg)
            return error_msg

    def _codebase_find_files(self, pattern: str) -> str:
        """Find files in codebase matching pattern."""
        try:
            task_id = str(uuid.uuid4())
            self._update_workflow_progress(task_id, f"Finding files: {pattern}", TaskType.CODE_ANALYSIS, 0.0)

            files = self.codebase_tool.find_files(pattern)
            self._update_workflow_progress(task_id, f"File search completed: {pattern}", TaskType.CODE_ANALYSIS, 100.0)

            return f"Files matching '{pattern}':\n\n{json.dumps(files, indent=2)}"

        except Exception as e:
            error_msg = f"Error finding files with pattern {pattern}: {e}"
            self._update_workflow_progress(task_id, f"File search failed: {pattern}", TaskType.CODE_ANALYSIS, 100.0, error_msg)
            return error_msg

    def _codebase_search(self, pattern: str, file_pattern: str = "*") -> str:
        """Search for patterns in code files."""
        try:
            task_id = str(uuid.uuid4())
            self._update_workflow_progress(task_id, f"Searching codebase: {pattern}", TaskType.CODE_ANALYSIS, 0.0)

            results = self.codebase_tool.search(pattern, file_pattern)
            self._update_workflow_progress(task_id, f"Codebase search completed: {pattern}", TaskType.CODE_ANALYSIS, 100.0)

            if results:
                formatted_results = []
                for result in results:
                    formatted_results.append({
                        "file": result["file"],
                        "matches": [
                            {
                                "line": match["line"],
                                "content": match["content"].strip(),
                                "context": [line.strip() for line in match.get("context", [])]
                            }
                            for match in result["matches"]
                        ]
                    })
                return f"Search results for '{pattern}':\n\n{json.dumps(formatted_results, indent=2)}"
            else:
                return f"No matches found for pattern '{pattern}'"

        except Exception as e:
            error_msg = f"Error searching codebase for pattern {pattern}: {e}"
            self._update_workflow_progress(task_id, f"Codebase search failed: {pattern}", TaskType.CODE_ANALYSIS, 100.0, error_msg)
            return error_msg

    def _codebase_analyze(self, path: str) -> str:
        """Analyze code file structure."""
        try:
            task_id = str(uuid.uuid4())
            self._update_workflow_progress(task_id, f"Analyzing file: {path}", TaskType.CODE_ANALYSIS, 0.0)

            analysis = self.codebase_tool.analyze(path)
            self._update_workflow_progress(task_id, f"File analysis completed: {path}", TaskType.CODE_ANALYSIS, 100.0)

            if analysis:
                formatted_analysis = {
                    "file": path,
                    "language": analysis.get("language", "unknown"),
                    "structure": analysis.get("structure", []),
                    "dependencies": analysis.get("dependencies", []),
                    "issues": analysis.get("issues", []),
                    "metrics": analysis.get("metrics", {})
                }
                return f"Analysis results for {path}:\n\n{json.dumps(formatted_analysis, indent=2)}"
            else:
                return f"No analysis results available for {path}"

        except Exception as e:
            error_msg = f"Error analyzing file {path}: {e}"
            self._update_workflow_progress(task_id, f"File analysis failed: {path}", TaskType.CODE_ANALYSIS, 100.0, error_msg)
            return error_msg

    def _codebase_dependencies(self) -> str:
        """Analyze project dependencies."""
        try:
            task_id = str(uuid.uuid4())
            self._update_workflow_progress(task_id, "Analyzing dependencies", TaskType.CODE_ANALYSIS, 0.0)

            dependencies = self.codebase_tool.analyze_dependencies()
            self._update_workflow_progress(task_id, "Dependencies analyzed", TaskType.CODE_ANALYSIS, 100.0)

            return f"Project dependencies:\n\n{json.dumps(dependencies, indent=2)}"

        except Exception as e:
            error_msg = f"Error analyzing dependencies: {e}"
            self._update_workflow_progress(task_id, "Dependencies analysis failed", TaskType.CODE_ANALYSIS, 100.0, error_msg)
            return error_msg

    def _codebase_metrics(self) -> str:
        """Get codebase metrics and statistics."""
        try:
            task_id = str(uuid.uuid4())
            self._update_workflow_progress(task_id, "Calculating metrics", TaskType.CODE_ANALYSIS, 0.0)

            metrics = self.codebase_tool.get_metrics()
            self._update_workflow_progress(task_id, "Metrics calculated", TaskType.CODE_ANALYSIS, 100.0)

            return f"Codebase metrics:\n\n{json.dumps(metrics, indent=2)}"

        except Exception as e:
            error_msg = f"Error calculating metrics: {e}"
            self._update_workflow_progress(task_id, "Metrics calculation failed", TaskType.CODE_ANALYSIS, 100.0, error_msg)
            return error_msg

    def _take_screenshot(self, path: Optional[str] = None) -> str:
        """Take a screenshot."""
        try:
            task_id = str(uuid.uuid4())
            self._update_workflow_progress(task_id, "Taking screenshot", TaskType.FILE_OPERATION, 0.0)

            screenshot_path = self.vision_tool.take_screenshot(path)
            self._update_workflow_progress(task_id, "Screenshot taken", TaskType.FILE_OPERATION, 100.0)

            return f"Screenshot saved to: {screenshot_path}"

        except Exception as e:
            error_msg = f"Error taking screenshot: {e}"
            self._update_workflow_progress(task_id, "Screenshot failed", TaskType.FILE_OPERATION, 100.0, error_msg)
            return error_msg

    def _analyze_image(self, path: str) -> str:
        """Analyze an image file."""
        try:
            task_id = str(uuid.uuid4())
            self._update_workflow_progress(task_id, f"Analyzing image: {path}", TaskType.CODE_ANALYSIS, 0.0)

            image = self.vision_tool.load_image(path)
            self._update_workflow_progress(task_id, f"Image analyzed: {path}", TaskType.CODE_ANALYSIS, 100.0)

            return (
                f"Image analysis for {path}:\n"
                f"Dimensions: {image.width}x{image.height}\n"
                f"Format: {image.format}\n"
                f"Mode: {image.mode}"
            )

        except Exception as e:
            error_msg = f"Error analyzing image {path}: {e}"
            self._update_workflow_progress(task_id, f"Image analysis failed: {path}", TaskType.CODE_ANALYSIS, 100.0, error_msg)
            return error_msg

    def _apply_patch(self, file_path: str, original_code: str, updated_code: str) -> str:
        """Apply a code patch to a file."""
        try:
            task_id = str(uuid.uuid4())
            self._update_workflow_progress(task_id, f"Applying patch: {file_path}", TaskType.REFACTORING, 0.0)

            success = self.patch_tool.apply(file_path, original_code, updated_code)

            if success:
                self._update_workflow_progress(task_id, f"Patch applied: {file_path}", TaskType.REFACTORING, 100.0)
                return f"Patch successfully applied to {file_path}"
            else:
                error_msg = f"Failed to apply patch to {file_path}"
                self._update_workflow_progress(task_id, f"Patch failed: {file_path}", TaskType.REFACTORING, 100.0, error_msg)
                return error_msg

        except Exception as e:
            error_msg = f"Error applying patch to {file_path}: {e}"
            self._update_workflow_progress(task_id, f"Patch error: {file_path}", TaskType.REFACTORING, 100.0, error_msg)
            return error_msg

    def _browser_open(self, url: str, action: Optional[str] = None) -> str:
        """Open a URL in browser and optionally perform an action."""
        try:
            task_id = str(uuid.uuid4())
            self._update_workflow_progress(task_id, f"Opening browser: {url}", TaskType.WEB_SEARCH, 0.0)

            result = self.browser_tool.open_url(url, action)
            self._update_workflow_progress(task_id, f"Browser opened: {url}", TaskType.WEB_SEARCH, 100.0)

            return f"Browser opened successfully: {url}\nResult: {result}"

        except Exception as e:
            error_msg = f"Error opening browser for {url}: {e}"
            self._update_workflow_progress(task_id, f"Browser open failed: {url}", TaskType.WEB_SEARCH, 100.0, error_msg)
            return error_msg

    def _rag_query(self, query: str, context: Optional[str] = None) -> str:
        """Query the RAG system for relevant information."""
        if not self._has_rag:
            return "RAG system is not available. Please install required dependencies (faiss-cpu, sentence-transformers)."

        try:
            task_id = str(uuid.uuid4())
            self._update_workflow_progress(task_id, f"RAG query: {query}", TaskType.CODE_ANALYSIS, 0.0)

            results = self.rag_tool.query(query, context)
            self._update_workflow_progress(task_id, f"RAG query completed: {query}", TaskType.CODE_ANALYSIS, 100.0)

            return f"RAG query results for '{query}':\n\n{json.dumps(results, indent=2)}"

        except Exception as e:
            error_msg = f"Error querying RAG system: {e}"
            self._update_workflow_progress(task_id, f"RAG query failed: {query}", TaskType.CODE_ANALYSIS, 100.0, error_msg)
            return error_msg

    def _register_advanced_tools(self):
        """Register advanced development tools."""
        # Advanced file operations
        advanced_file_tools = [
            ("advanced_grep", "Advanced grep with context and options.", self._advanced_grep, ["pattern"]),
            ("create_backup", "Create backup of a file.", self._create_backup, ["file_path"]),
            ("batch_rename", "Batch rename files using regex.", self._batch_rename, ["pattern", "replacement"]),
            ("calculate_checksums", "Calculate MD5 checksums for files.", self._calculate_checksums, []),
            ("create_terminal_session", "Create persistent terminal session.", self._create_terminal_session, ["session_id"]),
            ("execute_in_session", "Execute command in terminal session.", self._execute_in_session, ["session_id", "command"]),
            ("close_terminal_session", "Close terminal session.", self._close_terminal_session, ["session_id"]),
            ("create_project", "Create new project from template.", self._create_project, ["project_name", "template"]),
            ("analyze_code_complexity", "Analyze code complexity metrics.", self._analyze_code_complexity, ["file_path"]),
            ("find_code_smells", "Find potential code smells.", self._find_code_smells, ["file_path"]),
            ("generate_test_template", "Generate test template for file.", self._generate_test_template, ["file_path"]),
            ("run_tests", "Run tests with specified framework.", self._run_tests, []),
            ("generate_dockerfile", "Generate Dockerfile for project.", self._generate_dockerfile, ["project_type"]),
            ("generate_docker_compose", "Generate docker-compose.yml.", self._generate_docker_compose, ["services"]),
            ("generate_github_actions", "Generate GitHub Actions workflow.", self._generate_github_actions, ["project_type"]),
            ("create_workflow_plan", "Create intelligent workflow plan.", self._create_workflow_plan, ["user_input"]),
            ("get_workflow_progress", "Get workflow progress information.", self._get_workflow_progress, ["plan_id"]),
            ("predict_next_steps", "Predict next logical steps.", self._predict_next_steps, ["conversation_id"]),
            ("get_conversation_insights", "Get conversation insights.", self._get_conversation_insights, ["conversation_id"])
        ]

        for name, desc, func, required_params in advanced_file_tools:
            properties = {}
            for param in required_params:
                if param in ["pattern", "file_path", "session_id", "command", "project_name", "template", "project_type"]:
                    properties[param] = {"type": "string", "description": f"The {param} parameter."}
                elif param == "replacement":
                    properties[param] = {"type": "string", "description": "Replacement pattern for batch rename."}
                elif param == "services":
                    properties[param] = {"type": "array", "description": "List of services for docker-compose."}

            self.tool_manager.register_tool(Tool(
                name=name,
                description=desc,
                function=func,
                parameters={
                    "type": "object",
                    "properties": properties,
                    "required": required_params
                }
            ))

    def _advanced_grep(self, pattern: str, file_pattern: str = "*", context_lines: int = 3) -> str:
        """Advanced grep with context."""
        try:
            result = self.file_tool.advanced_grep(pattern, file_pattern, context_lines)
            return json.dumps(result, indent=2)
        except Exception as e:
            return f"Error in advanced grep: {e}"

    def _create_backup(self, file_path: str, backup_dir: Optional[str] = None) -> str:
        """Create backup of a file."""
        try:
            result = self.file_tool.create_backup(file_path, backup_dir)
            return json.dumps(result, indent=2)
        except Exception as e:
            return f"Error creating backup: {e}"

    def _batch_rename(self, pattern: str, replacement: str, file_pattern: str = "*") -> str:
        """Batch rename files using regex."""
        try:
            result = self.file_tool.batch_rename(pattern, replacement, file_pattern)
            return json.dumps(result, indent=2)
        except Exception as e:
            return f"Error in batch rename: {e}"

    def _calculate_checksums(self, file_pattern: str = "*") -> str:
        """Calculate checksums for files."""
        try:
            result = self.file_tool.calculate_checksums(file_pattern)
            return json.dumps(result, indent=2)
        except Exception as e:
            return f"Error calculating checksums: {e}"

    def _create_terminal_session(self, session_id: str, shell: str = "bash") -> str:
        """Create persistent terminal session."""
        try:
            result = self.file_tool.create_terminal_session(session_id, shell)
            return json.dumps(result, indent=2)
        except Exception as e:
            return f"Error creating terminal session: {e}"

    def _execute_in_session(self, session_id: str, command: str, timeout: float = 30.0) -> str:
        """Execute command in terminal session."""
        try:
            result = self.file_tool.execute_in_session(session_id, command, timeout)
            return json.dumps(result, indent=2)
        except Exception as e:
            return f"Error executing in session: {e}"

    def _close_terminal_session(self, session_id: str) -> str:
        """Close terminal session."""
        try:
            result = self.file_tool.close_terminal_session(session_id)
            return json.dumps(result, indent=2)
        except Exception as e:
            return f"Error closing terminal session: {e}"

    def _create_project(self, project_name: str, template: str, options: Optional[Dict[str, Any]] = None) -> str:
        """Create new project from template."""
        try:
            result = self.file_tool.create_project(project_name, template, options)
            return json.dumps(result, indent=2)
        except Exception as e:
            return f"Error creating project: {e}"

    def _analyze_code_complexity(self, file_path: str) -> str:
        """Analyze code complexity."""
        try:
            result = self.file_tool.analyze_code_complexity(file_path)
            return json.dumps(result, indent=2)
        except Exception as e:
            return f"Error analyzing code complexity: {e}"

    def _find_code_smells(self, file_path: str) -> str:
        """Find code smells."""
        try:
            result = self.file_tool.find_code_smells(file_path)
            return json.dumps(result, indent=2)
        except Exception as e:
            return f"Error finding code smells: {e}"

    def _generate_test_template(self, file_path: str, test_framework: str = "unittest") -> str:
        """Generate test template."""
        try:
            result = self.file_tool.generate_test_template(file_path, test_framework)
            return json.dumps(result, indent=2)
        except Exception as e:
            return f"Error generating test template: {e}"

    def _run_tests(self, test_pattern: str = "test_*.py", framework: str = "unittest") -> str:
        """Run tests."""
        try:
            result = self.file_tool.run_tests(test_pattern, framework)
            return json.dumps(result, indent=2)
        except Exception as e:
            return f"Error running tests: {e}"

    def _generate_dockerfile(self, project_type: str, options: Optional[Dict[str, Any]] = None) -> str:
        """Generate Dockerfile."""
        try:
            result = self.file_tool.generate_dockerfile(project_type, options)
            return json.dumps(result, indent=2)
        except Exception as e:
            return f"Error generating Dockerfile: {e}"

    def _generate_docker_compose(self, services: List[Dict[str, Any]]) -> str:
        """Generate docker-compose.yml."""
        try:
            result = self.file_tool.generate_docker_compose(services)
            return json.dumps(result, indent=2)
        except Exception as e:
            return f"Error generating docker-compose: {e}"

    def _generate_github_actions(self, project_type: str, options: Optional[Dict[str, Any]] = None) -> str:
        """Generate GitHub Actions workflow."""
        try:
            result = self.file_tool.generate_github_actions(project_type, options)
            return json.dumps(result, indent=2)
        except Exception as e:
            return f"Error generating GitHub Actions: {e}"

    def _create_workflow_plan(self, user_input: str, conversation_id: Optional[str] = None) -> str:
        """Create intelligent workflow plan."""
        try:
            # Create conversation context
            context = self.workflow_manager.create_conversation_context(user_input, conversation_id)

            # Get available tools
            available_tools = list(self.tool_manager.tools.keys())

            # Generate workflow plan
            plan = self.workflow_manager.generate_workflow_plan(context, available_tools)

            # Get predictions
            predictions = self.workflow_manager.predict_next_steps(context)

            result = {
                'success': True,
                'plan_id': plan.plan_id,
                'conversation_id': context.conversation_id,
                'plan_name': plan.name,
                'total_steps': len(plan.steps),
                'estimated_duration': plan.estimated_total_duration,
                'domain': context.domain,
                'complexity': context.complexity_level,
                'predictions': predictions,
                'steps': [
                    {
                        'step_id': step.step_id,
                        'name': step.name,
                        'description': step.description,
                        'tool_name': step.tool_name,
                        'estimated_duration': step.estimated_duration
                    }
                    for step in plan.steps
                ]
            }

            return json.dumps(result, indent=2)

        except Exception as e:
            return f"Error creating workflow plan: {e}"

    def _get_workflow_progress(self, plan_id: str) -> str:
        """Get workflow progress information."""
        try:
            progress = self.workflow_manager.get_workflow_progress(plan_id)
            return json.dumps(progress, indent=2)
        except Exception as e:
            return f"Error getting workflow progress: {e}"

    def _predict_next_steps(self, conversation_id: str) -> str:
        """Predict next logical steps."""
        try:
            if conversation_id not in self.workflow_manager.conversation_contexts:
                return json.dumps({'error': 'Conversation not found'})

            context = self.workflow_manager.conversation_contexts[conversation_id]
            predictions = self.workflow_manager.predict_next_steps(context)

            result = {
                'conversation_id': conversation_id,
                'domain': context.domain,
                'predictions': predictions,
                'confidence': 'high' if len(predictions) > 3 else 'medium'
            }

            return json.dumps(result, indent=2)

        except Exception as e:
            return f"Error predicting next steps: {e}"

    def _get_conversation_insights(self, conversation_id: str) -> str:
        """Get conversation insights."""
        try:
            insights = self.workflow_manager.get_conversation_insights(conversation_id)
            return json.dumps(insights, indent=2)
        except Exception as e:
            return f"Error getting conversation insights: {e}"

    def _get_default_system_prompt(self) -> str:
        """Get the default system prompt for the enhanced agent."""
        return """You are an Enhanced AI Agent with comprehensive capabilities including:

🔧 **Multi-threaded Execution**: Handle multiple tasks in parallel for maximum efficiency
🧠 **Context-Aware Auto-Refactoring**: Intelligently refactor code based on context and best practices
🔮 **Predictive Prefetching**: Anticipate user needs and prepare suggestions in advance
💻 **Terminal Command Execution**: Execute shell commands with full error handling and output capture
📁 **Advanced File Operations**: Read, write, search, grep, copy, move files with comprehensive error handling
🌐 **Web Integration**: Search, fetch, scrape web content and download files
🔍 **Codebase Analysis**: Analyze code structure, dependencies, metrics, and find patterns
👁️ **Vision Capabilities**: Take screenshots and analyze images
🔄 **Patch Management**: Apply code patches and refactoring changes
🌐 **Browser Automation**: Open URLs and interact with web pages
🧮 **RAG System**: Query knowledge base for relevant information
📊 **Workflow Tracking**: Track progress, predict next steps, and provide status updates

**Key Features:**
- Execute one step at a time with thorough analysis
- Provide clear status updates and progress tracking
- Predict and suggest next logical steps
- Handle errors gracefully with detailed reporting
- Support full-stack development across multiple languages
- Maintain conversation context and chat history
- Optimize performance with intelligent caching and threading

**Available Tools:** shell_execute, file_read, file_write, file_append, file_delete, file_list, file_search, file_grep, file_copy, file_move, file_permissions, code_execute, code_analyze, web_search, web_fetch, web_scrape, web_download, codebase_find_files, codebase_search, codebase_analyze, codebase_dependencies, codebase_metrics, vision_screenshot, vision_analyze_image, patch_apply, browser_open, rag_query

Use these tools to provide comprehensive assistance with any development task. Always execute one step at a time, analyze results thoroughly, and plan the next step based on your analysis."""

    def get_workflow_status(self) -> Dict[str, Any]:
        """Get current workflow status and progress."""
        with self.lock:
            return {
                "conversation_id": self.workflow_context.conversation_id,
                "user_intent": self.workflow_context.user_intent,
                "current_task": self.workflow_context.current_task.__dict__ if self.workflow_context.current_task else None,
                "completed_tasks": [task.__dict__ for task in self.workflow_context.completed_tasks],
                "pending_tasks": [task.__dict__ for task in self.workflow_context.pending_tasks],
                "predictions": self.workflow_context.predictions,
                "total_completed": len(self.workflow_context.completed_tasks),
                "total_pending": len(self.workflow_context.pending_tasks)
            }

    def predict_next_steps(self, user_input: str) -> List[str]:
        """Predict next steps based on user input and context."""
        if self.prefetcher:
            try:
                return self.prefetcher.predict_next_actions(
                    user_input,
                    "user_request",
                    self.workflow_context.context_data
                )
            except Exception as e:
                logger.warning(f"Could not predict next steps: {e}")

        # Fallback basic predictions
        return [
            "Analyze the request thoroughly",
            "Execute the appropriate tools",
            "Provide detailed results",
            "Suggest follow-up actions"
        ]

    async def process_message_async(self, message: str, conversation: Optional[Conversation] = None) -> str:
        """Process a message asynchronously with enhanced capabilities."""
        # Update workflow context
        self.workflow_context.user_intent = message
        self.workflow_context.chat_history.append({
            "role": "user",
            "content": message,
            "timestamp": time.time()
        })

        # Predict next steps
        predictions = self.predict_next_steps(message)
        self.workflow_context.predictions = predictions

        # Process the message (this would integrate with the existing message processing)
        # For now, return a placeholder response
        response = f"Enhanced Agent received: {message}\nPredicted next steps: {', '.join(predictions)}"

        self.workflow_context.chat_history.append({
            "role": "assistant",
            "content": response,
            "timestamp": time.time()
        })

        return response

# Legacy Agent class for backward compatibility
Agent = EnhancedAgent
