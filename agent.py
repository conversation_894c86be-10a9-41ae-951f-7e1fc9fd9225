"""
Agent module for the Advanced AI Agent.
"""

import re
import json
import time
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Tuple, Generator, Callable

from models import ModelManager
from conversation import Conversation, Message, ConversationManager
from tools import (
    ShellTool, FileTool, CodeTool, WebTool, CodebaseTool, VisionTool,
    PatchTool, BrowserTool, RagTool, SearchAPI,
    WebScraperTool, InformationSynthesizer, WebInfoManager
)
from tools.tool_manager import <PERSON><PERSON>, ToolManager
from core.ai_code_assistant import AICodeAssistant, AssistantRequest, AssistantResponse
from utils import get_logger

# Get the logger
logger = get_logger()

class Agent:
    """The AI agent."""

    def __init__(
        self,
        model_manager: ModelManager,
        conversation_manager: ConversationManager,
        workspace_dir: Optional[Path] = None,
        system_prompt: Optional[str] = None
    ):
        """Initialize the agent.

        Args:
            model_manager: The model manager to use.
            conversation_manager: The conversation manager to use.
            workspace_dir: The workspace directory to use. If None, will use the current directory.
            system_prompt: The system prompt to use. If None, will use a default prompt.
        """
        self.model_manager = model_manager
        self.conversation_manager = conversation_manager
        self.workspace_dir = workspace_dir or Path.cwd()
        self._has_rag = False  # Initialize _has_rag attribute

        # Initialize tool manager
        self.tool_manager = ToolManager()

        # Initialize tools first, then set the system prompt
        self._initialize_tools()

        # Initialize AI Code Assistant
        self.ai_code_assistant = AICodeAssistant(self.model_manager, self.workspace_dir)

        # Set the system prompt
        if system_prompt is None:
            self.system_prompt = self._get_default_system_prompt()
        else:
            self.system_prompt = system_prompt

    def _initialize_tools(self):
        """Initialize and register the tools with the ToolManager."""
        # Initialize individual tool instances
        self.shell_tool = ShellTool(self.workspace_dir)
        self.file_tool = FileTool(self.workspace_dir)
        self.code_tool = CodeTool()
        self.web_tool = WebTool()
        self.codebase_tool = CodebaseTool(self.workspace_dir)
        self.vision_tool = VisionTool(self.workspace_dir)
        self.patch_tool = PatchTool(self.workspace_dir)
        self.browser_tool = BrowserTool()
        # Initialize search_api first since it's used by other tools
        self.search_api = SearchAPI()
        self.web_scraper = WebScraperTool()
        self.info_synthesizer = InformationSynthesizer()
        self.web_info_manager = WebInfoManager()

        # Initialize RAG tool if dependencies are available
        try:
            import importlib.util
            has_faiss = importlib.util.find_spec("faiss") is not None
            has_sentence_transformers = importlib.util.find_spec("sentence_transformers") is not None

            if has_faiss and has_sentence_transformers:
                self.rag_tool = RagTool(self.workspace_dir)
                self._has_rag = True
            else:
                self._has_rag = False
        except Exception:
            self._has_rag = False

        # Register tools with the ToolManager
        self._register_shell_tools()
        self._register_file_tools()
        self._register_code_tools()
        self._register_web_tools()
        self._register_codebase_tools()
        self._register_vision_tools()
        self._register_patch_tools()
        self._register_browser_tools()
        self._register_scrape_tools()
        self._register_info_tools()
        self._register_ai_assistant_tool()
        if self._has_rag:
            self._register_rag_tool()
        if self._has_rag:
            self._register_rag_tool()

    def _register_shell_tools(self):
        self.tool_manager.register_tool(Tool(
            name="shell",
            description="Execute shell commands on the system.",
            function=self._execute_shell,
            parameters={
                "type": "object",
                "properties": {
                    "command": {"type": "string", "description": "The shell command to execute."}
                },
                "required": ["command"]
            }
        ))

    def _execute_shell(self, args: str) -> str:
        """Execute a shell command.

        Args:
            args: The command to execute.

        Returns:
            The command output or error message.
        """
        if not args.strip():
            return "Error: No command specified."

        try:
            stdout, stderr, return_code = self.shell_tool.execute(args)
            
            if return_code == 0:
                if stdout:
                    return f"Command executed successfully:\n\n{stdout}"
                return "Command executed successfully (no output)"
            else:
                return f"Command failed (exit code {return_code}):\n\n{stderr}"
        except Exception as e:
            return f"Error executing command: {e}"

    def _register_file_tools(self):
        self.tool_manager.register_tool(Tool(
            name="file_read",
            description="Read the content of a file.",
            function=self._file_read,
            parameters={
                "type": "object",
                "properties": {
                    "path": {"type": "string", "description": "The path to the file to read."}
                },
                "required": ["path"]
            }
        ))

    def _register_code_tools(self):
        self.tool_manager.register_tool(Tool(
            name="code_execute",
            description="Execute code in a specified programming language.",
            function=self._execute_code,
            parameters={
                "type": "object",
                "properties": {
                    "language": {"type": "string", "description": "The programming language (e.g., 'python', 'javascript')."},
                    "code": {"type": "string", "description": "The code to execute."}
                },
                "required": ["language", "code"]
            }
        ))

    def _register_web_tools(self):
        self.tool_manager.register_tool(Tool(
            name="web_search",
            description="Search the web for information.",
            function=self._execute_web_search,
            parameters={
                "type": "object",
                "properties": {
                    "query": {"type": "string", "description": "The search query."}
                },
                "required": ["query"]
            }
        ))
        self.tool_manager.register_tool(Tool(
            name="web_fetch",
            description="Fetch content from a given URL.",
            function=self._execute_web_fetch,
            parameters={
                "type": "object",
                "properties": {
                    "url": {"type": "string", "description": "The URL to fetch."}
                },
                "required": ["url"]
            }
        ))

    def _execute_web_search(self, query: str) -> str:
        """Execute a web search operation.
        
        Args:
            query: The search query
            
        Returns:
            Search results in JSON format
        """
        try:
            results = self.web_tool.search(query)
            return json.dumps(results, indent=2)
        except Exception as e:
            return f"Error performing web search: {str(e)}"

    def _execute_web_fetch(self, url: str) -> str:
        """Fetch content from a URL.
        
        Args:
            url: The URL to fetch
            
        Returns:
            Fetched content with metadata
        """
        try:
            content, metadata = self.web_tool.fetch_url(url)
            return (
                f"URL: {url}\n"
                f"Title: {metadata.get('title', 'N/A')}\n"
                f"Content length: {len(content)} characters\n\n"
                f"{content[:2000]}..."  # Return first 2000 chars
            )
        except Exception as e:
            return f"Error fetching URL: {str(e)}"

    def _register_codebase_tools(self):
        self.tool_manager.register_tool(Tool(
            name="codebase_find_files",
            description="Find files in the codebase matching a pattern.",
            function=self._execute_codebase_find_files,
            parameters={
                "type": "object",
                "properties": {
                    "pattern": {"type": "string", "description": "The pattern to search for (e.g., '*.py', 'config.json'). Defaults to '*'."}
                },
                "required": []
            }
        ))
        self.tool_manager.register_tool(Tool(
            name="codebase_find_code_files",
            description="Find all code files in the codebase.",
            function=self._execute_codebase_find_code_files,
            parameters={"type": "object", "properties": {}, "required": []}
        ))
        self.tool_manager.register_tool(Tool(
            name="codebase_search",
            description="Search for a pattern within code files.",
            function=self._execute_codebase_search,
            parameters={
                "type": "object",
                "properties": {
                    "pattern": {"type": "string", "description": "The regex pattern to search for."},
                    "file_pattern": {"type": "string", "description": "Optional glob pattern to filter files (e.g., '*.js'). Defaults to '*'."}
                },
                "required": ["pattern"]
            }
        ))
        self.tool_manager.register_tool(Tool(
            name="codebase_analyze",
            description="Analyze a specific code file for structure and potential issues.",
            function=self._execute_codebase_analyze,
            parameters={
                "type": "object",
                "properties": {
                    "path": {"type": "string", "description": "The path to the code file to analyze."}
                },
                "required": ["path"]
            }
        ))

    def _execute_codebase_find_files(self, pattern: str = "*") -> str:
        """Find files in the codebase matching a pattern.

        Args:
            pattern: The pattern to search for (e.g., '*.py'). If empty, searches for all files.

        Returns:
            List of matching files with their paths.
        """
        try:
            files = self.codebase_tool.find_files(pattern)
            if files:
                return f"Found {len(files)} files matching '{pattern}':\n\n{json.dumps(files, indent=2)}"
            else:
                return f"No files found matching pattern: {pattern}"
        except Exception as e:
            return f"Error finding files: {e}"

    def _execute_codebase_find_code_files(self) -> str:
        """Find all code files in the codebase.

        Returns:
            List of code files with their paths.
        """
        try:
            files = self.codebase_tool.find_code_files()
            if files:
                return f"Found {len(files)} code files:\n\n{json.dumps(files, indent=2)}"
            else:
                return "No code files found in the codebase."
        except Exception as e:
            return f"Error finding code files: {e}"

    def _execute_codebase_search(self, pattern: str, file_pattern: str = "*") -> str:
        """Search for a pattern within code files.

        Args:
            pattern: The regex pattern to search for.
            file_pattern: Optional glob pattern to filter files (e.g., '*.js'). Defaults to '*'."

        Returns:
            Search results with matching lines and context.
        """
        try:
            results = self.codebase_tool.search(pattern, file_pattern)
            if results:
                formatted_results = []
                for result in results:
                    formatted_results.append({
                        "file": result["file"],
                        "matches": [
                            {
                                "line": match["line"],
                                "content": match["content"].strip(),
                                "context": [line.strip() for line in match["context"]]
                            }
                            for match in result["matches"]
                        ]
                    })
                return f"Search results for '{pattern}' in '{file_pattern}':\n\n{json.dumps(formatted_results, indent=2)}"
            else:
                return f"No matches found for pattern '{pattern}' in files matching '{file_pattern}'"
        except Exception as e:
            return f"Error searching codebase: {e}"

    def _execute_codebase_analyze(self, path: str) -> str:
        """Analyze a specific code file for structure and potential issues.

        Args:
            path: The path to the code file to analyze.

        Returns:
            Analysis results including structure, dependencies, and potential issues.
        """
        if not path.strip():
            return "Error: No file path specified."

        try:
            analysis = self.codebase_tool.analyze(path)
            if analysis:
                formatted_analysis = {
                    "file": path,
                    "language": analysis.get("language", "unknown"),
                    "structure": analysis.get("structure", []),
                    "dependencies": analysis.get("dependencies", []),
                    "issues": analysis.get("issues", []),
                    "metrics": analysis.get("metrics", {})
                }
                return f"Analysis results for {path}:\n\n{json.dumps(formatted_analysis, indent=2)}"
            else:
                return f"No analysis results available for {path}"
        except Exception as e:
            return f"Error analyzing file: {e}"

    def _register_vision_tools(self):
        self.tool_manager.register_tool(Tool(
            name="vision_take_screenshot",
            description="Take a screenshot of the current screen.",
            function=self._execute_vision_take_screenshot,
            parameters={
                "type": "object",
                "properties": {
                    "path": {"type": "string", "description": "Optional path to save the screenshot. Defaults to a temporary file."}
                },
                "required": []
            }
        ))
        self.tool_manager.register_tool(Tool(
            name="vision_load_image",
            description="Load an image from a given path.",
            function=self._execute_vision_load_image,
            parameters={
                "type": "object",
                "properties": {
                    "path": {"type": "string", "description": "The path to the image file."}
                },
                "required": ["path"]
            }
        ))

    def _execute_vision_take_screenshot(self, path: str = None) -> str:
        """Take a screenshot of the current screen.

        Args:
            path: Optional path to save the screenshot. If empty, uses a temporary file.

        Returns:
            Path where the screenshot was saved.
        """
        try:
            screenshot_path = self.vision_tool.take_screenshot(path)
            return f"Screenshot saved to: {screenshot_path}"
        except Exception as e:
            return f"Error taking screenshot: {e}"

    def _execute_vision_load_image(self, path: str) -> str:
        """Load an image from a given path.

        Args:
            path: The path to the image file to load.

        Returns:
            Image metadata including dimensions and format.
        """
        if not path.strip():
            return "Error: No image path specified."

        try:
            image = self.vision_tool.load_image(path)
            return (
                f"Image loaded successfully:\n"
                f"Path: {path}\n"
                f"Dimensions: {image.width}x{image.height}\n"
                f"Format: {image.format}"
            )
        except Exception as e:
            return f"Error loading image: {e}"

    def _register_patch_tools(self):
        """Register patch-related tools with the tool manager."""
        self.tool_manager.register_tool(Tool(
            name="patch_apply",
            description="Apply a patch to a file.",
            function=self._execute_patch_apply,
            parameters={
                "type": "object",
                "properties": {
                    "file_path": {"type": "string", "description": "The path to the file to patch."},
                    "original_code": {"type": "string", "description": "The original code snippet to replace."},
                    "updated_code": {"type": "string", "description": "The new code snippet to insert."}
                },
                "required": ["file_path", "original_code", "updated_code"]
            }
        ))

    def _execute_patch_apply(self, args: str) -> str:
        """Apply a patch to a file.

        Args:
            args: The arguments containing file path, original code, and updated code.

        Returns:
            Confirmation of the patch being applied.
        """
        # Parse the arguments (file_path, original_code, updated_code)
        args_parts = args.strip().split(maxsplit=2)
        if len(args_parts) < 3:
            return "Error: All three arguments (file_path, original_code, updated_code) must be specified."

        file_path = args_parts[0]
        original_code = args_parts[1]
        updated_code = args_parts[2]

        try:
            success = self.patch_tool.apply(file_path, original_code, updated_code)
            if success:
                return f"Patch successfully applied to {file_path}"
            else:
                return f"Failed to apply patch to {file_path}"
        except Exception as e:
            return f"Error applying patch: {e}"

    def _register_browser_tools(self):
        """Register browser-related tools with the tool manager."""
        self.tool_manager.register_tool(Tool(
            name="browser_read_url",
            description="Browse a URL and get its content.",
            function=self._execute_browser_read_url,
            parameters={
                "type": "object",
                "properties": {
                    "url": {"type": "string", "description": "The URL to browse."}
                },
                "required": ["url"]
            }
        ))

    def _execute_browser_read_url(self, args: str) -> str:
        """Browse a URL and get its content.

        Args:
            args: The URL to browse.

        Returns:
            The content of the URL with metadata.
        """
        if not args.strip():
            return "Error: No URL specified."

        try:
            content, metadata = self.browser_tool.read_url(args)
            return (
                f"Content from {args}:\n"
                f"Title: {metadata.get('title', 'N/A')}\n"
                f"Content length: {len(content)} characters\n\n"
                f"{content[:2000]}..."  # Return first 2000 chars to avoid huge responses
            )
        except Exception as e:
            return f"Error browsing URL: {e}"

    def _execute_code(self, args: str) -> str:
        """Execute code in various programming languages.

        Args:
            args: The language and code to execute separated by a delimiter.

        Returns:
            The execution result or error message.
        """
        # Parse the arguments (language and code)
        args_parts = args.strip().split(maxsplit=1)
        if len(args_parts) < 2:
            return "Error: Both language and code must be specified."

        language = args_parts[0].lower()
        code = args_parts[1]

        try:
            # Execute the code
            stdout, stderr, return_code = self.code_tool.execute(code, language)
            
            if return_code == 0:
                if stdout:
                    return f"Code executed successfully:\n\n{stdout}"
                return "Code executed successfully (no output)"
            else:
                return f"Code execution failed (exit code {return_code}):\n\n{stderr}"
        except Exception as e:
            return f"Error executing code: {e}"

    def _execute_file(self, args: str) -> str:
        """Execute a file operation.

        Args:
            args: The arguments for the file operation.

        Returns:
            The result of the operation.
        """
        # Parse the arguments
        args_parts = args.strip().split(maxsplit=1)
        if not args_parts:
            return "Error: No file operation specified."

        operation = args_parts[0]

        if operation == "read":
            # Read a file
            if len(args_parts) < 2:
                return "Error: No file path specified."

            file_path = args_parts[1]

            try:
                content = self.file_tool.read(file_path)
                return f"File content:\n\n{content}"
            except Exception as e:
                return f"Error reading file: {e}"

        elif operation == "write":
            # Write a file
            if len(args_parts) < 2:
                return "Error: No file path specified."

            # Parse file path and content
            parts = args_parts[1].split(maxsplit=1)
            if len(parts) < 2:
                return "Error: No file content specified."

            file_path = parts[0]
            content = parts[1]

            try:
                self.file_tool.write(file_path, content)
                return f"File written successfully: {file_path}"
            except Exception as e:
                return f"Error writing file: {e}"

        elif operation == "delete":
            # Delete a file
            if len(args_parts) < 2:
                return "Error: No file path specified."

            file_path = args_parts[1]

            try:
                self.file_tool.delete(file_path)
                return f"File deleted successfully: {file_path}"
            except Exception as e:
                return f"Error deleting file: {e}"

        elif operation == "list":
            # List files
            path = args_parts[1] if len(args_parts) > 1 else "."
            
            try:
                files = self.file_tool.list(path)
                return f"Files in {path}:\n\n{json.dumps(files, indent=2)}"
            except Exception as e:
                return f"Error listing files: {e}"

        else:
            return f"Error: Unknown file operation: {operation}"
        self.tool_manager.register_tool(Tool(
            name="file_write",
            description="Write content to a file. Overwrites if the file exists.",
            function=self._file_write,
            parameters={
                "type": "object",
                "properties": {
                    "path": {"type": "string", "description": "The path to the file to write."},
                    "content": {"type": "string", "description": "The content to write to the file."}
                },
                "required": ["path", "content"]
            }
        ))
        self.tool_manager.register_tool(Tool(
            name="file_append",
            description="Append content to an existing file.",
            function=self._file_append,
            parameters={
                "type": "object",
                "properties": {
                    "path": {"type": "string", "description": "The path to the file to append to."},
                    "content": {"type": "string", "description": "The content to append to the file."}
                },
                "required": ["path", "content"]
            }
        ))
        self.tool_manager.register_tool(Tool(
            name="file_delete",
            description="Delete a file.",
            function=self._file_delete,
            parameters={
                "type": "object",
                "properties": {
                    "path": {"type": "string", "description": "The path to the file to delete."}
                },
                "required": ["path"]
            }
        ))
        self.tool_manager.register_tool(Tool(
            name="file_list",
            description="List files and directories in a given path.",
            function=self._file_list,
            parameters={
                "type": "object",
                "properties": {
                    "path": {"type": "string", "description": "The directory path to list. Defaults to current directory."}
                },
                "required": []
            }
        ))
        self.tool_manager.register_tool(Tool(
            name="file_search",
            description="Search for files matching a pattern.",
            function=self._file_search,
            parameters={
                "type": "object",
                "properties": {
                    "pattern": {"type": "string", "description": "The pattern to search for (e.g., '*.py', 'report.txt')."}
                },
                "required": ["pattern"]
            }
        ))
        self.tool_manager.register_tool(Tool(
            name="file_grep",
            description="Search for a pattern within files.",
            function=self._file_grep,
            parameters={
                "type": "object",
                "properties": {
                    "pattern": {"type": "string", "description": "The regex pattern to search for."},
                    "file_pattern": {"type": "string", "description": "Optional glob pattern to filter files (e.g., '*.py'). Defaults to '*'."}
                },
                "required": ["pattern"]
            }
        ))

    def _file_read(self, path: str) -> str:
        """Read the content of a file."""
        try:
            return self.file_tool.read(path)
        except Exception as e:
            return f"Error reading file: {e}"

    def _file_write(self, path: str, content: str) -> str:
        """Write content to a file. Overwrites if the file exists."""
        try:
            self.file_tool.write(path, content)
            return f"File written successfully: {path}"
        except Exception as e:
            return f"Error writing file: {e}"

    def _file_append(self, path: str, content: str) -> str:
        """Append content to an existing file."""
        try:
            self.file_tool.append(path, content)
            return f"Content appended successfully to: {path}"
        except Exception as e:
            return f"Error appending to file: {e}"

    def _file_delete(self, path: str) -> str:
        """Delete a file."""
        try:
            self.file_tool.delete(path)
            return f"File deleted successfully: {path}"
        except Exception as e:
            return f"Error deleting file: {e}"

    def _file_list(self, path: str = ".") -> str:
        """List files and directories in a given path."""
        try:
            files = self.file_tool.list(path)
            return json.dumps(files, indent=2)
        except Exception as e:
            return f"Error listing files: {e}"

    def _file_search(self, pattern: str) -> str:
        """Search for files matching a pattern."""
        try:
            files = self.file_tool.search(pattern)
            return json.dumps(files, indent=2)
        except Exception as e:
            return f"Error searching for files: {e}"

    def _file_grep(self, pattern: str, file_pattern: str = "*") -> str:
        """Search for a pattern within files."""
        try:
            results = self.file_tool.grep(pattern, file_pattern)
            return json.dumps(results, indent=2)
        except Exception as e:
            return f"Error grepping files: {e}"

def _execute_file(self, args: str) -> str:
    """Execute a file operation.

    Args:
        args: The arguments for the file operation.

    Returns:
        The result of the operation.
    """
    # Parse the arguments
    args_parts = args.strip().split(maxsplit=1)
    if not args_parts:
        return "Error: No file operation specified."

    operation = args_parts[0]

    if operation == "read":
        # Read a file
        if len(args_parts) < 2:
            return "Error: No file path specified."

        file_path = args_parts[1]

        try:
            content = self._file_read(file_path)
            return f"File content:\n\n{content}"
        except Exception as e:
            return f"Error reading file: {e}"

    elif operation == "write":
        # Write a file
        if len(args_parts) < 2:
            return "Error: No file path specified."

        # Parse the file path and content
        # Use a more robust approach to split only on the first space
        # This ensures file content with spaces is handled correctly
        parts_text = args_parts[1]
        try:
            # Find the first space after the file path
            space_index = parts_text.index(' ')
            file_path = parts_text[:space_index].strip()
            content = parts_text[space_index+1:].strip()
        except ValueError:
            # No space found, treat the whole string as the file path
            return "Error: No file content specified."

        if not file_path:
            return "Error: No file path specified."

        if not content:
            return "Error: No file content specified."

        try:
            # Create parent directories if they don't exist
            path_obj = Path(self.workspace_dir) / file_path
            path_obj.parent.mkdir(parents=True, exist_ok=True)

            # Write the file
            self._file_write(file_path, content)
            return f"File written successfully: {file_path}"
        except Exception as e:
            return f"Error writing file: {e}"

    elif operation == "append":
        # Append to a file
        if len(args_parts) < 2:
            return "Error: No file path specified."

        # Parse the file path and content using a more robust approach
        parts_text = args_parts[1]
        try:
            # Find the first space after the file path
            space_index = parts_text.index(' ')
            file_path = parts_text[:space_index].strip()
            content = parts_text[space_index+1:].strip()
        except ValueError:
            # No space found, treat the whole string as the file path
            return "Error: No file content specified."

        if not file_path:
            return "Error: No file path specified."

        if not content:
            return "Error: No file content specified."

        try:
            # Create parent directories if they don't exist
            path_obj = Path(self.workspace_dir) / file_path
            path_obj.parent.mkdir(parents=True, exist_ok=True)

            # Append to the file
            self._file_append(file_path, content)
            return f"Content appended successfully to: {file_path}"
        except Exception as e:
            return f"Error appending to file: {e}"

    elif operation == "delete":
        # Delete a file
        if len(args_parts) < 2:
            return "Error: No file path specified."

        file_path = args_parts[1]

        try:
            self._file_delete(file_path)
            return f"File deleted successfully: {file_path}"
        except Exception as e:
            return f"Error deleting file: {e}"

    elif operation == "list":
        # List files in a directory
        directory = args_parts[1] if len(args_parts) > 1 else "."

        try:
            files = self._file_list(directory)
            return f"Files in {directory}:\n\n{files}"
        except Exception as e:
            return f"Error listing files: {e}"

    elif operation == "search":
        # Search for files
        if len(args_parts) < 2:
            return "Error: No search pattern specified."

        pattern = args_parts[1]

        try:
            files = self._file_search(pattern)
            return f"Files matching {pattern}:\n\n{files}"
        except Exception as e:
            return f"Error searching for files: {e}"

    elif operation == "grep":
        # Search for a pattern in files
        if len(args_parts) < 2:
            return "Error: No grep arguments specified."

        # Parse the pattern and file pattern
        grep_parts = args_parts[1].split(maxsplit=1)
        if len(grep_parts) < 2:
            pattern = grep_parts[0]
            file_pattern = "*"
        else:
            pattern = grep_parts[0]
            file_pattern = grep_parts[1]

        try:
            results = self._file_grep(pattern, file_pattern)
            return f"Grep results for {pattern} in {file_pattern}:\n\n{results}"
        except Exception as e:
            return f"Error grepping files: {e}"

    else:
        return f"Error: Unknown file operation: {operation}"


    def _register_code_tools(self):
        self.tool_manager.register_tool(Tool(
            name="code_execute",
            description="Execute code in a specified programming language.",
            function=self._execute_code,
            parameters={
                "type": "object",
                "properties": {
                    "language": {"type": "string", "description": "The programming language (e.g., 'python', 'javascript')."},
                    "code": {"type": "string", "description": "The code to execute."}
                },
                "required": ["language", "code"]
            }
        ))

    def _execute_code(self, args: str) -> str:
        """Execute code in various programming languages.

        Args:
            args: The language and code to execute separated by a delimiter.

        Returns:
            The execution result or error message.
        """
        # Parse the arguments (language and code)
        args_parts = args.strip().split(maxsplit=1)
        if len(args_parts) < 2:
            return "Error: Both language and code must be specified."

        language = args_parts[0].lower()
        code = args_parts[1]

        try:
            # Execute the code
            stdout, stderr, return_code = self.code_tool.execute(code, language)
            
            if return_code == 0:
                if stdout:
                    return f"Code executed successfully:\n\n{stdout}"
                return "Code executed successfully (no output)"
            else:
                return f"Code execution failed (exit code {return_code}):\n\n{stderr}"
        except Exception as e:
            return f"Error executing code: {e}"

    def _register_web_tools(self):
        self.tool_manager.register_tool(Tool(
            name="web_search",
            description="Search the web for information.",
            function=self._execute_web_search,
            parameters={
                "type": "object",
                "properties": {
                    "query": {"type": "string", "description": "The search query."}
                },
                "required": ["query"]
            }
        ))
        self.tool_manager.register_tool(Tool(
            name="web_fetch",
            description="Fetch content from a given URL.",
            function=self._execute_web_fetch,
            parameters={
                "type": "object",
                "properties": {
                    "url": {"type": "string", "description": "The URL to fetch."}
                },
                "required": ["url"]
            }
        ))

    def _execute_web_search(self, args: str) -> str:
        """Execute a web search operation.

        Args:
            args: The search query.

        Returns:
            The search results.
        """
        if not args.strip():
            return "Error: No search query specified."

        try:
            results = self.web_tool.search(args)
            if results:
                return f"Web search results for '{args}':\n\n{json.dumps(results, indent=2)}"
            else:
                return "No results found for the search query."
        except Exception as e:
            return f"Error performing web search: {e}"

    def _execute_web_fetch(self, args: str) -> str:
        """Fetch content from a URL.

        Args:
            args: The URL to fetch.

        Returns:
            The fetched content.
        """
        if not args.strip():
            return "Error: No URL specified."

        try:
            content, metadata = self.web_tool.fetch_url(args)
            return (
                f"Content from {args}:\n"
                f"Title: {metadata.get('title', 'N/A')}\n"
                f"Content length: {len(content)} characters\n\n"
                f"{content[:2000]}..."  # Return first 2000 chars to avoid huge responses
            )
        except Exception as e:
            return f"Error fetching URL: {e}"

    def _execute_web_search(self, args: str) -> str:
        """Execute a web search operation.

        Args:
            args: The search query.

        Returns:
            The search results.
        """
        if not args.strip():
            return "Error: No search query specified."

        try:
            results = self.web_tool.search(args)
            if results:
                return f"Web search results for '{args}':\n\n{json.dumps(results, indent=2)}"
            else:
                return "No results found for the search query."
        except Exception as e:
            return f"Error performing web search: {e}"

    def _execute_web_fetch(self, args: str) -> str:
        """Fetch content from a URL.

        Args:
            args: The URL to fetch.

        Returns:
            The fetched content.
        """
        if not args.strip():
            return "Error: No URL specified."

        try:
            content, metadata = self.web_tool.fetch_url(args)
            return (
                f"Content from {args}:\n"
                f"Title: {metadata.get('title', 'N/A')}\n"
                f"Content length: {len(content)} characters\n\n"
                f"{content[:2000]}..."  # Return first 2000 chars to avoid huge responses
            )
        except Exception as e:
            return f"Error fetching URL: {e}"

    def _register_codebase_tools(self):
        self.tool_manager.register_tool(Tool(
            name="codebase_find_files",
            description="Find files in the codebase matching a pattern.",
            function=self._execute_codebase_find_files,
            parameters={
                "type": "object",
                "properties": {
                    "pattern": {"type": "string", "description": "The pattern to search for (e.g., '*.py', 'config.json'). Defaults to '*'."}
                },
                "required": []
            }
        ))
        self.tool_manager.register_tool(Tool(
            name="codebase_find_code_files",
            description="Find all code files in the codebase.",
            function=self._execute_codebase_find_code_files,
            parameters={"type": "object", "properties": {}, "required": []}
        ))
        self.tool_manager.register_tool(Tool(
            name="codebase_search",
            description="Search for a pattern within code files.",
            function=self._execute_codebase_search,
            parameters={
                "type": "object",
                "properties": {
                    "pattern": {"type": "string", "description": "The regex pattern to search for."},
                    "file_pattern": {"type": "string", "description": "Optional glob pattern to filter files (e.g., '*.js'). Defaults to '*'."}
                },
                "required": ["pattern"]
            }
        ))
        self.tool_manager.register_tool(Tool(
            name="codebase_analyze",
            description="Analyze a specific code file for structure and potential issues.",
            function=self._execute_codebase_analyze,
            parameters={
                "type": "object",
                "properties": {
                    "path": {"type": "string", "description": "The path to the code file to analyze."}
                },
                "required": ["path"]
            }
        ))

    def _execute_codebase_find_files(self, args: str) -> str:
        """Find files in the codebase matching a pattern.

        Args:
            args: The pattern to search for (e.g., '*.py'). If empty, searches for all files.

        Returns:
            List of matching files with their paths.
        """
        pattern = args.strip() if args.strip() else "*"
        
        try:
            files = self.codebase_tool.find_files(pattern)
            if files:
                return f"Found {len(files)} files matching '{pattern}':\n\n{json.dumps(files, indent=2)}"
            else:
                return f"No files found matching pattern: {pattern}"
        except Exception as e:
            return f"Error finding files: {e}"

    def _execute_codebase_find_code_files(self, args: str) -> str:
        """Find all code files in the codebase.

        Args:
            args: Unused parameter (kept for consistency with other tool methods).

        Returns:
            List of code files with their paths.
        """
        try:
            files = self.codebase_tool.find_code_files()
            if files:
                return f"Found {len(files)} code files:\n\n{json.dumps(files, indent=2)}"
            else:
                return "No code files found in the codebase."
        except Exception as e:
            return f"Error finding code files: {e}"

    def _execute_codebase_search(self, args: str) -> str:
        """Search for a pattern within code files.

        Args:
            args: The search arguments containing pattern and optional file pattern.

        Returns:
            Search results with matching lines and context.
        """
        # Parse the arguments
        args_parts = args.strip().split(maxsplit=1)
        if not args_parts:
            return "Error: No search pattern specified."

        pattern = args_parts[0]
        file_pattern = args_parts[1] if len(args_parts) > 1 else "*"

        try:
            results = self.codebase_tool.search(pattern, file_pattern)
            if results:
                formatted_results = []
                for result in results:
                    formatted_results.append({
                        "file": result["file"],
                        "matches": [
                            {
                                "line": match["line"],
                                "content": match["content"].strip(),
                                "context": [line.strip() for line in match["context"]]
                            }
                            for match in result["matches"]
                        ]
                    })
                return f"Search results for '{pattern}' in '{file_pattern}':\n\n{json.dumps(formatted_results, indent=2)}"
            else:
                return f"No matches found for pattern '{pattern}' in files matching '{file_pattern}'"
        except Exception as e:
            return f"Error searching codebase: {e}"

    def _execute_codebase_analyze(self, args: str) -> str:
        """Analyze a specific code file for structure and potential issues.

        Args:
            args: The path to the code file to analyze.

        Returns:
            Analysis results including structure, dependencies, and potential issues.
        """
        if not args.strip():
            return "Error: No file path specified."

        try:
            analysis = self.codebase_tool.analyze(args)
            if analysis:
                formatted_analysis = {
                    "file": args,
                    "language": analysis.get("language", "unknown"),
                    "structure": analysis.get("structure", []),
                    "dependencies": analysis.get("dependencies", []),
                    "issues": analysis.get("issues", []),
                    "metrics": analysis.get("metrics", {})
                }
                return f"Analysis results for {args}:\n\n{json.dumps(formatted_analysis, indent=2)}"
            else:
                return f"No analysis results available for {args}"
        except Exception as e:
            return f"Error analyzing file: {e}"

    def _execute_codebase_find_files(self, args: str) -> str:
        """Find files in the codebase matching a pattern.

        Args:
            args: The pattern to search for (e.g., '*.py'). If empty, searches for all files.

        Returns:
            List of matching files with their paths.
        """
        pattern = args.strip() if args.strip() else "*"
        
        try:
            files = self.codebase_tool.find_files(pattern)
            if files:
                return f"Found {len(files)} files matching '{pattern}':\n\n{json.dumps(files, indent=2)}"
            else:
                return f"No files found matching pattern: {pattern}"
        except Exception as e:
            return f"Error finding files: {e}"

    def _execute_codebase_find_code_files(self, args: str) -> str:
        """Find all code files in the codebase.

        Args:
            args: Unused parameter (kept for consistency with other tool methods).

        Returns:
            List of code files with their paths.
        """
        try:
            files = self.codebase_tool.find_code_files()
            if files:
                return f"Found {len(files)} code files:\n\n{json.dumps(files, indent=2)}"
            else:
                return "No code files found in the codebase."
        except Exception as e:
            return f"Error finding code files: {e}"

    def _execute_codebase_search(self, args: str) -> str:
        """Search for a pattern within code files.

        Args:
            args: The search arguments containing pattern and optional file pattern.

        Returns:
            Search results with matching lines and context.
        """
        # Parse the arguments
        args_parts = args.strip().split(maxsplit=1)
        if not args_parts:
            return "Error: No search pattern specified."

        pattern = args_parts[0]
        file_pattern = args_parts[1] if len(args_parts) > 1 else "*"

        try:
            results = self.codebase_tool.search(pattern, file_pattern)
            if results:
                formatted_results = []
                for result in results:
                    formatted_results.append({
                        "file": result["file"],
                        "matches": [
                            {
                                "line": match["line"],
                                "content": match["content"].strip(),
                                "context": [line.strip() for line in match["context"]]
                            }
                            for match in result["matches"]
                        ]
                    })
                return f"Search results for '{pattern}' in '{file_pattern}':\n\n{json.dumps(formatted_results, indent=2)}"
            else:
                return f"No matches found for pattern '{pattern}' in files matching '{file_pattern}'"
        except Exception as e:
            return f"Error searching codebase: {e}"

    def _execute_codebase_analyze(self, args: str) -> str:
        """Analyze a specific code file for structure and potential issues.

        Args:
            args: The path to the code file to analyze.

        Returns:
            Analysis results including structure, dependencies, and potential issues.
        """
        if not args.strip():
            return "Error: No file path specified."

        try:
            analysis = self.codebase_tool.analyze(args)
            if analysis:
                formatted_analysis = {
                    "file": args,
                    "language": analysis.get("language", "unknown"),
                    "structure": analysis.get("structure", []),
                    "dependencies": analysis.get("dependencies", []),
                    "issues": analysis.get("issues", []),
                    "metrics": analysis.get("metrics", {})
                }
                return f"Analysis results for {args}:\n\n{json.dumps(formatted_analysis, indent=2)}"
            else:
                return f"No analysis results available for {args}"
        except Exception as e:
            return f"Error analyzing file: {e}"

    def _register_vision_tools(self):
        self.tool_manager.register_tool(Tool(
            name="vision_take_screenshot",
            description="Take a screenshot of the current screen.",
            function=self._execute_vision_take_screenshot,
            parameters={
                "type": "object",
                "properties": {
                    "path": {"type": "string", "description": "Optional path to save the screenshot. Defaults to a temporary file."}
                },
                "required": []
            }
        ))
        self.tool_manager.register_tool(Tool(
            name="vision_load_image",
            description="Load an image from a given path.",
            function=self._execute_vision_load_image,
            parameters={
                "type": "object",
                "properties": {
                    "path": {"type": "string", "description": "The path to the image file."}
                },
                "required": ["path"]
            }
        ))

    def _execute_vision_take_screenshot(self, args: str) -> str:
        """Take a screenshot of the current screen.

        Args:
            args: Optional path to save the screenshot. If empty, uses a temporary file.

        Returns:
            Path where the screenshot was saved.
        """
        path = args.strip() if args.strip() else None
        
        try:
            screenshot_path = self.vision_tool.take_screenshot(path)
            return f"Screenshot saved to: {screenshot_path}"
        except Exception as e:
            return f"Error taking screenshot: {e}"

    def _execute_vision_load_image(self, args: str) -> str:
        """Load an image from a given path.

        Args:
            args: The path to the image file to load.

        Returns:
            Image metadata including dimensions and format.
        """
        if not args.strip():
            return "Error: No image path specified."

        try:
            image = self.vision_tool.load_image(args)
            return (
                f"Image loaded successfully:\n"
                f"Path: {args}\n"
                f"Dimensions: {image.width}x{image.height}\n"
                f"Format: {image.format}"
            )
        except Exception as e:
            return f"Error loading image: {e}"

    def _execute_vision_take_screenshot(self, args: str) -> str:
        """Take a screenshot of the current screen.

        Args:
            args: Optional path to save the screenshot. If empty, uses a temporary file.

        Returns:
            Path where the screenshot was saved.
        """
        path = args.strip() if args.strip() else None
        
        try:
            screenshot_path = self.vision_tool.take_screenshot(path)
            return f"Screenshot saved to: {screenshot_path}"
        except Exception as e:
            return f"Error taking screenshot: {e}"

    def _execute_vision_load_image(self, args: str) -> str:
        """Load an image from a given path.

        Args:
            args: The path to the image file to load.

        Returns:
            Image metadata including dimensions and format.
        """
        if not args.strip():
            return "Error: No image path specified."

        try:
            image = self.vision_tool.load_image(args)
            return (
                f"Image loaded successfully:\n"
                f"Path: {args}\n"
                f"Dimensions: {image.width}x{image.height}\n"
                f"Format: {image.format}"
            )
        except Exception as e:
            return f"Error loading image: {e}"



    def _register_patch_tools(self):
        self.tool_manager.register_tool(Tool(
            name="patch_apply",
            description="Apply a patch to a file.",
            function=self._execute_patch_apply,
            parameters={
                "type": "object",
                "properties": {
                    "file_path": {"type": "string", "description": "The path to the file to patch."},
                    "original_code": {"type": "string", "description": "The original code snippet to replace."},
                    "updated_code": {"type": "string", "description": "The new code snippet to insert."}
                },
                "required": ["file_path", "original_code", "updated_code"]
            }
        ))

    def _execute_patch_apply(self, args: str) -> str:
        """Apply a patch to a file.

        Args:
            args: The arguments containing file path, original code, and updated code.

        Returns:
            Confirmation of the patch being applied.
        """
        # Parse the arguments (file_path, original_code, updated_code)
        args_parts = args.strip().split(maxsplit=2)
        if len(args_parts) < 3:
            return "Error: All three arguments (file_path, original_code, updated_code) must be specified."

        file_path = args_parts[0]
        original_code = args_parts[1]
        updated_code = args_parts[2]

        try:
            success = self.patch_tool.apply(file_path, original_code, updated_code)
            if success:
                return f"Patch successfully applied to {file_path}"
            else:
                return f"Failed to apply patch to {file_path}"
        except Exception as e:
            return f"Error applying patch: {e}"

    def _execute_patch_apply(self, args: str) -> str:
        """Apply a patch to a file.

        Args:
            args: The arguments containing file path, original code, and updated code.

        Returns:
            Confirmation of the patch being applied.
        """
        # Parse the arguments (file_path, original_code, updated_code)
        args_parts = args.strip().split(maxsplit=2)
        if len(args_parts) < 3:
            return "Error: All three arguments (file_path, original_code, updated_code) must be specified."

        file_path = args_parts[0]
        original_code = args_parts[1]
        updated_code = args_parts[2]

        try:
            success = self.patch_tool.apply(file_path, original_code, updated_code)
            if success:
                return f"Patch successfully applied to {file_path}"
            else:
                return f"Failed to apply patch to {file_path}"
        except Exception as e:
            return f"Error applying patch: {e}"

    def _register_browser_tools(self):
        self.tool_manager.register_tool(Tool(
            name="browser_read_url",
            description="Browse a URL and get its content.",
            function=self._execute_browser_read_url,
            parameters={
                "type": "object",
                "properties": {
                    "url": {"type": "string", "description": "The URL to browse."}
                },
                "required": ["url"]
            }
        ))

    def _execute_browser_read_url(self, args: str) -> str:
        """Browse a URL and get its content.

        Args:
            args: The URL to browse.

        Returns:
            The content of the URL with metadata.
        """
        if not args.strip():
            return "Error: No URL specified."

        try:
            content, metadata = self.browser_tool.read_url(args)
            return (
                f"Content from {args}:\n"
                f"Title: {metadata.get('title', 'N/A')}\n"
                f"Content length: {len(content)} characters\n\n"
                f"{content[:2000]}..."  # Return first 2000 chars to avoid huge responses
            )
        except Exception as e:
            return f"Error browsing URL: {e}"

    def _execute_browser_read_url(self, args: str) -> str:
        """Browse a URL and get its content.

        Args:
            args: The URL to browse.

        Returns:
            The content of the URL with metadata.
        """
        if not args.strip():
            return "Error: No URL specified."

        try:
            content, metadata = self.browser_tool.read_url(args)
            return (
                f"Content from {args}:\n"
                f"Title: {metadata.get('title', 'N/A')}\n"
                f"Content length: {len(content)} characters\n\n"
                f"{content[:2000]}..."  # Return first 2000 chars to avoid huge responses
            )
        except Exception as e:
            return f"Error browsing URL: {e}"

    def _register_search_tools(self):
        """Register search-related tools with the tool manager."""
        self.tool_manager.register_tool(Tool(
            name="search_web",
            description="Perform a reliable web search using multiple fallback methods.",
            function=self._execute_search_web,
            parameters={
                "type": "object",
                "properties": {
                    "query": {"type": "string", "description": "The search query."},
                    "num_results": {"type": "integer", "description": "Number of results to return. Defaults to 5.", "default": 5}
                },
                "required": ["query"]
            }
        ))

    def _execute_search_web(self, args: str) -> str:
        """Perform a web search using multiple fallback methods.

        Args:
            args: The search query and optional number of results.

        Returns:
            The search results in JSON format.
        """
        # Parse the arguments (query and optional num_results)
        args_parts = args.strip().split(maxsplit=1)
        if not args_parts:
            return "Error: No search query specified."

        query = args_parts[0]
        num_results = 5  # Default
        
        if len(args_parts) > 1:
            try:
                num_results = int(args_parts[1])
            except ValueError:
                pass  # Use default if invalid number

        try:
            results = self.search_api.search(query, num_results=num_results)
            if results:
                return f"Search results for '{query}':\n\n{json.dumps(results, indent=2)}"
            else:
                return f"No results found for query: {query}"
        except Exception as e:
            return f"Error performing web search: {e}"

    def _register_search_tools(self):
        """Register search-related tools with the tool manager."""
        self.tool_manager.register_tool(Tool(
            name="search_web",
            description="Perform a reliable web search using multiple fallback methods.",
            function=self._execute_search_web,
            parameters={
                "type": "object",
                "properties": {
                    "query": {"type": "string", "description": "The search query."},
                    "num_results": {"type": "integer", "description": "Number of results to return. Defaults to 5.", "default": 5}
                },
                "required": ["query"]
            }
        ))

    def _register_scrape_tools(self):
        """Register web scraping tools with the tool manager."""
        self.tool_manager.register_tool(Tool(
            name="scrape_web",
            description="Scrape content from a website with advanced fallback mechanisms.",
            function=self._execute_scrape_web,
            parameters={
                "type": "object",
                "properties": {
                    "url": {"type": "string", "description": "The URL to scrape."}
                },
                "required": ["url"]
            }
        ))

    def _execute_scrape_web(self, args: str) -> str:
        """Scrape content from a website using multiple fallback methods.

        Args:
            args: The URL to scrape.

        Returns:
            The scraped content in a structured format.
        """
        if not args.strip():
            return "Error: No URL specified for scraping."

        try:
            # Scrape the URL with fallback methods
            scraped_data = self.web_scraper.scrape(args)
            
            if scraped_data:
                return f"Scraped content from {args}:\n\n{json.dumps(scraped_data, indent=2)}"
            else:
                return f"No content could be scraped from {args}"
        except Exception as e:
            return f"Error scraping website: {e}"

    def _register_info_tools(self):
        """Register information processing tools with the tool manager."""
        self.tool_manager.register_tool(Tool(
            name="info_retrieve",
            description="Retrieve and synthesize information about a topic.",
            function=self._execute_info_retrieve,
            parameters={
                "type": "object",
                "properties": {
                    "topic": {"type": "string", "description": "The topic to retrieve information about."}
                },
                "required": ["topic"]
            }
        ))
        self.tool_manager.register_tool(Tool(
            name="info_extract",
            description="Extract key information from a given text.",
            function=self._execute_info_extract,
            parameters={
                "type": "object",
                "properties": {
                    "text": {"type": "string", "description": "The text from which to extract information."}
                },
                "required": ["text"]
            }
        ))
        self.tool_manager.register_tool(Tool(
            name="info_format",
            description="Format information in a structured manner (e.g., markdown, JSON).",
            function=self._execute_info_format,
            parameters={
                "type": "object",
                "properties": {
                    "information": {"type": "string", "description": "The information to format."},
                    "format_type": {"type": "string", "description": "The desired format (e.g., 'markdown', 'json')."}
                },
                "required": ["information", "format_type"]
            }
        ))

    def _execute_info_retrieve(self, args: str) -> str:
        """Retrieve and synthesize information about a topic.

        Args:
            args: The topic to retrieve information about.

        Returns:
            The synthesized information about the topic.
        """
        if not args.strip():
            return "Error: No topic specified for information retrieval."

        try:
            result = self.info_synthesizer.retrieve(args)
            if result:
                return f"Information about '{args}':\n\n{result}"
            else:
                return f"No information found about topic: {args}"
        except Exception as e:
            return f"Error retrieving information: {e}"

    def _execute_info_extract(self, args: str) -> str:
        """Extract key information from a given text.

        Args:
            args: The text to extract information from.

        Returns:
            The extracted key information in a structured format.
        """
        if not args.strip():
            return "Error: No text provided for information extraction."

        try:
            extracted_info = self.info_synthesizer.extract(args)
            return f"Extracted information:\n\n{json.dumps(extracted_info, indent=2)}"
        except Exception as e:
            return f"Error extracting information: {e}"

    def _execute_info_format(self, args: str) -> str:
        """Format information in a specified structure.

        Args:
            args: The information and format type separated by a delimiter.

        Returns:
            The formatted information.
        """
        # Parse the arguments (information and format_type)
        args_parts = args.strip().split(maxsplit=1)
        if len(args_parts) < 2:
            return "Error: Both information and format type must be specified."

        information = args_parts[0]
        format_type = args_parts[1].lower()

        try:
            formatted_info = self.info_synthesizer.format(information, format_type)
            return f"Formatted information ({format_type}):\n\n{formatted_info}"
        except Exception as e:
            return f"Error formatting information: {e}"

    def _register_ai_assistant_tool(self):
        self.tool_manager.register_tool(Tool(
            name="ai_assistant",
            description="Advanced AI code analysis, generation, and optimization.",
            function=self._execute_ai_assistant,
            parameters={
                "type": "object",
                "properties": {
                    "request_type": {"type": "string", "description": "Type of request (e.g., 'analyze', 'generate', 'optimize')."},
                    "content": {"type": "string", "description": "Content for the request (e.g., code, problem description)."},
                    "language": {"type": "string", "description": "Programming language if applicable."}
                },
                "required": ["request_type", "content"]
            }
        ))

    def _execute_ai_assistant(self, args: str) -> str:
        """Execute an AI assistant operation.

        Args:
            args: The request type and content separated by a delimiter.

        Returns:
            The result of the AI assistant operation.
        """
        # Parse the arguments (request_type and content)
        args_parts = args.strip().split(maxsplit=1)
        if len(args_parts) < 2:
            return "Error: Both request type and content must be specified."

        request_type = args_parts[0]
        content = args_parts[1]

        try:
            # Create an assistant request
            request = AssistantRequest(
                request_type=request_type,
                content=content,
                language="python"  # Default to python if not specified
            )

            # Process the request
            response = self.ai_code_assistant.process_request(request)

            if response.success:
                return f"AI Assistant response ({request_type}):\n\n{response.content}"
            else:
                return f"AI Assistant error: {response.error_message}"
        except Exception as e:
            return f"Error processing AI assistant request: {e}"

    def _execute_ai_assistant(self, args: str) -> str:
        """Execute an AI assistant operation.

        Args:
            args: The arguments for the AI assistant operation.

        Returns:
            The result of the operation.
        """
        # Parse the arguments
        args_parts = args.strip().split(maxsplit=1)
        if not args_parts:
            return "Error: No AI assistant operation specified."

        request_type = args_parts[0]
        content = args_parts[1] if len(args_parts) > 1 else ""

        try:
            # Create an assistant request
            request = AssistantRequest(
                request_type=request_type,
                content=content,
                language="python"  # Default to python if not specified
            )

            # Process the request
            response = self.ai_code_assistant.process_request(request)

            if response.success:
                return f"AI Assistant response ({request_type}):\n\n{response.content}"
            else:
                return f"AI Assistant error: {response.error_message}"
        except Exception as e:
            return f"Error processing AI assistant request: {e}"

    def _register_rag_tool(self):
        self.tool_manager.register_tool(Tool(
            name="rag",
            description="Retrieval-augmented generation for answering questions based on a knowledge base.",
            function=self._execute_rag,
            parameters={
                "type": "object",
                "properties": {
                    "query": {"type": "string", "description": "The query for RAG."}
                },
                "required": ["query"]
            }
        ))

    def _execute_rag(self, args: str) -> str:
        """Execute a RAG operation to answer questions based on knowledge base.

        Args:
            args: The query to process with RAG.

        Returns:
            The response from RAG system with relevant information.
        """
        if not args.strip():
            return "Error: No query specified for RAG operation."

        if not self._has_rag:
            return "Error: RAG functionality is not available. Required dependencies (faiss, sentence-transformers) not found."

        try:
            # Process the RAG query
            result = self.rag_tool.query(args)
            
            if result:
                return f"RAG response for '{args}':\n\n{result}"
            else:
                return f"No relevant information found for query: {args}"
        except Exception as e:
            return f"Error processing RAG query: {e}"

    def _execute_rag(self, args: str) -> str:
        """Execute a RAG operation.

        Args:
            args: The arguments for the RAG operation.

        Returns:
            The result of the operation.
        """
        if not args.strip():
            return "Error: No query specified for RAG operation."

        if not self._has_rag:
            return "Error: RAG functionality is not available. Required dependencies (faiss, sentence-transformers) not found."

        try:
            # Process the RAG query
            result = self.rag_tool.query(args)
            
            if result:
                return f"RAG response:\n\n{result}"
            else:
                return "No relevant information found in the knowledge base."
        except Exception as e:
            return f"Error processing RAG query: {e}"

    def _get_default_system_prompt(self) -> str:
        """Get the default system prompt.

        Returns:
            The default system prompt.
        """
        tools_list_str = ""
        for tool in self.tool_manager.get_all_tools():
            tools_list_str += f"- {tool.name}: {tool.description}\n"

        tool_examples = self.tool_manager.generate_tool_prompt_examples()

        return f"""
You are an advanced AI coding agent that can help with various tasks.
You have access to the following tools:
{tools_list_str}

When you need to use a tool, use the following JSON format:
```tool_code
{{
  "tool_name": "tool_name_here",
  "parameters": {{
    "param1": "value1",
    "param2": "value2"
  }}
}}
```

Here are examples of how to use each tool:
{tool_examples}

Always provide clear explanations of what you're doing and why.
If you're not sure about something, ask for clarification.
Be helpful, accurate, and concise.
"""

    def _prepare_conversation_history(self, conversation: Conversation) -> List[Dict[str, Any]]:
        """Prepare conversation history for the model.

        Args:
            conversation: The conversation to prepare history from.

        Returns:
            List of message dictionaries for the model.
        """
        # Convert conversation messages to the format expected by the model
        history = []

        # Add system prompt as the first message if not already in conversation
        if not any(msg.role == "system" for msg in conversation.messages):
            history.append({
                "role": "system",
                "content": self.system_prompt
            })

        # Add all messages from the conversation
        for message in conversation.messages:
            # Skip tool messages for now as they're handled separately
            if message.role not in ["tool", "tool_result"]:
                history.append({
                    "role": message.role,
                    "content": message.content
                })

        return history

    def process_message(self, message: str, conversation: Optional[Conversation] = None) -> str:
        """Process a message from the user.

        Args:
            message: The message from the user.
            conversation: The conversation to add the message to. If None, will use the current conversation.

        Returns:
            The response from the agent.
        """
        # Get the conversation
        if conversation is None:
            if self.conversation_manager.current_conversation is None:
                self.conversation_manager.new_conversation()
            conversation = self.conversation_manager.current_conversation

        # Add the user message to the conversation
        conversation.add_message("user", message)

        # Prepare conversation history
        conversation_history = self._prepare_conversation_history(conversation)

        # Generate the response using conversation history
        response = self.model_manager.generate(
            prompt=message,
            system_prompt=self.system_prompt,
            conversation_history=conversation_history
        )

        # Process the response for tool calls
        processed_response = self._process_response(response, conversation)

        # Add the assistant message to the conversation
        conversation.add_message("assistant", processed_response)

        # Check if we should summarize the conversation
        if (conversation.get_message_count() >= self.conversation_manager.auto_summarize_threshold and
            not conversation.summary):
            # Try to summarize the conversation
            self.conversation_manager.summarize_conversation(conversation, self.model_manager)

        # Save the conversation
        self.conversation_manager.save_conversation()

        # Analyze the tool result and provide a status update
        tool_pattern = r"```(\w+)\n(.*?)```"
        tool_matches = re.finditer(tool_pattern, response, re.DOTALL)
        if tool_matches:
            tool_result = conversation.messages[-1].content
            match = next(tool_matches)
            tool_name = match.group(1)
            tool_args = match.group(2).strip()
            status_update = self._analyze_tool_result(tool_name, tool_args, tool_result)
            return f"{processed_response}\n\n{status_update}"

        return processed_response

    def stream_process_message(
        self,
        message: str,
        conversation: Optional[Conversation] = None,
        callback: Optional[Callable[[str], None]] = None
    ) -> Generator[str, None, None]:
        """Process a message from the user and stream the response.

        Args:
            message: The message from the user.
            conversation: The conversation to add the message to. If None, will use the current conversation.
            callback: A callback function to call with each chunk of the response.

        Yields:
            Chunks of the response.
        """
        # Get the conversation
        if conversation is None:
            if self.conversation_manager.current_conversation is None:
                self.conversation_manager.new_conversation()
            conversation = self.conversation_manager.current_conversation

        # Add the user message to the conversation
        conversation.add_message("user", message)

        # Prepare conversation history
        conversation_history = self._prepare_conversation_history(conversation)

        # Generate the response using conversation history
        response_chunks = []
        for chunk in self.model_manager.stream_generate(
            prompt=message,
            system_prompt=self.system_prompt,
            conversation_history=conversation_history
        ):
            response_chunks.append(chunk)
            if callback:
                callback(chunk)
            yield chunk

        # Process the response for tool calls
        response = "".join(response_chunks)
        processed_response = self._process_response(response, conversation)

        # If the response was processed (tool calls), yield the processed response
        if processed_response != response:
            if callback:
                callback(processed_response)
            yield processed_response

        # Check for tool calls and analyze the result
        if tool_matches:
            try:
                tool_result = conversation.messages[-1].content
                match = next(tool_matches)
                tool_name = match.group(1)
                tool_args = match.group(2).strip()
                status_update = self._analyze_tool_result(tool_name, tool_args, tool_result)
                yield f"\n\n{status_update}"
            except StopIteration:
                pass

        # Add the assistant message to the conversation
        conversation.add_message("assistant", processed_response)

        # Check if we should summarize the conversation
        if (conversation.get_message_count() >= self.conversation_manager.auto_summarize_threshold and
            not conversation.summary):
            # Try to summarize the conversation
            self.conversation_manager.summarize_conversation(conversation, self.model_manager)

        # Save the conversation
        self.conversation_manager.save_conversation()

    def _process_response(self, response: str, conversation: Conversation) -> str:
        """Process a response for tool calls.

        Args:
            response: The response to process.
            conversation: The conversation to add tool messages to.

        Returns:
            The processed response.
        """
        # Check for tool calls
        tool_pattern = r"```(\w+)\n(.*?)```"
        tool_matches = re.finditer(tool_pattern, response, re.DOTALL)

        # Process the first tool call
        if tool_matches:
            match = next(tool_matches)
            tool_name = match.group(1)
            tool_args = match.group(2).strip()

            # Check if the tool exists
            if tool_name in self.tools:
                # Execute the tool
                tool_result = self.tools[tool_name](tool_args)

                # Add the tool call and result to the conversation
                conversation.add_message("tool", f"```{tool_name}\n{tool_args}\n```")
                conversation.add_message("tool_result", tool_result)

                # Replace the tool call with the result in the response
                response = response.replace(match.group(0), f"```{tool_name}\n{tool_args}\n```\n\n{tool_result}")

                # Analyze the tool result and provide a status update
                status_update = self._analyze_tool_result(tool_name, tool_args, tool_result)
                response = f"{response}\n\n{status_update}"

        return response

    def _analyze_tool_result(self, tool_name: str, tool_args: str, tool_result: str) -> str:
        """Analyze the results of a tool call and provide a clear status update.

        Args:
            tool_name: The name of the tool that was called.
            tool_args: The arguments that were passed to the tool.
            tool_result: The result of the tool call.

        Returns:
            A clear status update for the user.
        """
        # Analyze the tool result and provide a clear status update
        if "Error" in tool_result:
            return f"Tool {tool_name} failed with the following error: {tool_result}"
        else:
            return f"Tool {tool_name} executed successfully. Result: {tool_result}"

    def _execute_shell(self, command: str) -> str:
        """Execute a shell command.

        Args:
            command: The command to execute.

        Returns:
            The result of the command.
        """
        try:
            stdout, stderr, return_code = self.shell_tool.execute(command)

            if return_code == 0:
                if stdout.strip():
                    return f"Command executed successfully:\n\n{stdout}"
                else:
                    return "Command executed successfully."
            else:
                return f"Command failed with return code {return_code}:\n\n{stderr}"

        except Exception as e:
            return f"Error executing command: {e}"

    def _execute_file(self, args: str) -> str:
        """Execute a file operation.

        Args:
            args: The arguments for the file operation.

        Returns:
            The result of the operation.
        """
        # Parse the arguments
        args_parts = args.strip().split(maxsplit=1)
        if not args_parts:
            return "Error: No file operation specified."

        operation = args_parts[0]

        if operation == "read":
            # Read a file
            if len(args_parts) < 2:
                return "Error: No file path specified."

            file_path = args_parts[1]

            try:
                content = self.file_tool.read_file(file_path)
                return f"File content:\n\n{content}"
            except Exception as e:
                return f"Error reading file: {e}"

        elif operation == "write":
            # Write a file
            if len(args_parts) < 2:
                return "Error: No file path specified."

            # Parse the file path and content
            # Use a more robust approach to split only on the first space
            # This ensures file content with spaces is handled correctly
            parts_text = args_parts[1]
            try:
                # Find the first space after the file path
                space_index = parts_text.index(' ')
                file_path = parts_text[:space_index].strip()
                content = parts_text[space_index+1:].strip()
            except ValueError:
                # No space found, treat the whole string as the file path
                return "Error: No file content specified."

            if not file_path:
                return "Error: No file path specified."

            if not content:
                return "Error: No file content specified."

            try:
                # Create parent directories if they don't exist
                path_obj = Path(self.workspace_dir) / file_path
                path_obj.parent.mkdir(parents=True, exist_ok=True)

                # Write the file
                self.file_tool.write_file(file_path, content)
                return f"File written successfully: {file_path}"
            except Exception as e:
                return f"Error writing file: {e}"

        elif operation == "append":
            # Append to a file
            if len(args_parts) < 2:
                return "Error: No file path specified."

            # Parse the file path and content using a more robust approach
            parts_text = args_parts[1]
            try:
                # Find the first space after the file path
                space_index = parts_text.index(' ')
                file_path = parts_text[:space_index].strip()
                content = parts_text[space_index+1:].strip()
            except ValueError:
                # No space found, treat the whole string as the file path
                return "Error: No file content specified."

            if not file_path:
                return "Error: No file path specified."

            if not content:
                return "Error: No file content specified."

            try:
                # Create parent directories if they don't exist
                path_obj = Path(self.workspace_dir) / file_path
                path_obj.parent.mkdir(parents=True, exist_ok=True)

                # Append to the file
                self.file_tool.append_file(file_path, content)
                return f"Content appended successfully to: {file_path}"
            except Exception as e:
                return f"Error appending to file: {e}"

        elif operation == "delete":
            # Delete a file
            if len(args_parts) < 2:
                return "Error: No file path specified."

            file_path = args_parts[1]

            try:
                self.file_tool.delete_file(file_path)
                return f"File deleted successfully: {file_path}"
            except Exception as e:
                return f"Error deleting file: {e}"

        elif operation == "list":
            # List files in a directory
            directory = args_parts[1] if len(args_parts) > 1 else "."

            try:
                files = self.file_tool.list_files(directory)
                return f"Files in {directory}:\n\n{json.dumps(files, indent=2)}"
            except Exception as e:
                return f"Error listing files: {e}"

        elif operation == "search":
            # Search for files
            if len(args_parts) < 2:
                return "Error: No search pattern specified."

            pattern = args_parts[1]

            try:
                files = self.file_tool.search_files(pattern)
                return f"Files matching {pattern}:\n\n{json.dumps(files, indent=2)}"
            except Exception as e:
                return f"Error searching for files: {e}"

        elif operation == "grep":
            # Search for a pattern in files
            if len(args_parts) < 2:
                return "Error: No grep arguments specified."

            # Parse the pattern and file pattern
            grep_parts = args_parts[1].split(maxsplit=1)
            if len(grep_parts) < 2:
                pattern = grep_parts[0]
                file_pattern = "*"
            else:
                pattern = grep_parts[0]
                file_pattern = grep_parts[1]

            try:
                results = self.file_tool.grep_files(pattern, file_pattern)
                return f"Grep results for {pattern} in {file_pattern}:\n\n{json.dumps(results, indent=2)}"
            except Exception as e:
                return f"Error grepping files: {e}"

        else:
            return f"Error: Unknown file operation: {operation}"

    def _execute_code(self, args: str) -> str:
        """Execute code in various programming languages.

        Args:
            args: The arguments for the code execution.

        Returns:
            The result of the operation.
        """
        # Parse the arguments
        args_parts = args.strip().split("\n", 1)
        if len(args_parts) < 2:
            return "Error: No code or language specified."

        language = args_parts[0].strip().lower()
        code = args_parts[1]

        # Check if the language is supported
        supported_languages = self.code_tool.get_supported_languages()
        if language not in supported_languages:
            return f"Error: Unsupported language '{language}'. Supported languages: {', '.join(supported_languages)}"

        # Log the execution attempt
        logger.info(f"Executing {language} code...")

        try:
            # Execute the code with proper error handling
            stdout, stderr, return_code = self.code_tool.execute(code, language)

            # Format the response based on the execution result
            if return_code != 0:
                # Execution failed
                error_message = stderr.strip() if stderr.strip() else "Unknown error"
                logger.error(f"{language.capitalize()} execution failed with return code {return_code}: {error_message}")
                return f"{language.capitalize()} execution error (return code {return_code}):\n\n{stderr}"
            else:
                # Execution succeeded
                if stdout.strip():
                    logger.info(f"{language.capitalize()} execution succeeded with output")
                    return f"{language.capitalize()} execution output:\n\n{stdout}"
                else:
                    logger.info(f"{language.capitalize()} execution succeeded with no output")
                    return f"{language.capitalize()} code executed successfully with no output."

        except Exception as e:
            # Handle any exceptions during execution
            error_details = str(e)
            logger.error(f"Error executing {language} code: {error_details}")

            # Provide a more detailed error message
            if "timeout" in error_details.lower():
                return f"Error: {language.capitalize()} code execution timed out after 30 seconds. Please optimize your code or break it into smaller parts."
            elif "not found" in error_details.lower():
                return f"Error: Required interpreter or compiler for {language} not found. Please ensure {language} is installed on the system."
            else:
                return f"Error executing {language} code: {error_details}"

    def _execute_web(self, args: str) -> str:
        """Execute a web operation.

        Args:
            args: The arguments for the web operation.

        Returns:
            The result of the operation.
        """
        # Parse the arguments
        args_parts = args.strip().split(maxsplit=1)
        if not args_parts:
            return "Error: No web operation specified."

        operation = args_parts[0]

        if operation == "search":
            # Search the web
            if len(args_parts) < 2:
                return "Error: No search query specified."

            query = args_parts[1]

            try:
                results = self.web_tool.search(query)
                return f"Search results for '{query}':\n\n{json.dumps(results, indent=2)}"
            except Exception as e:
                return f"Error searching the web: {e}"

        elif operation == "fetch":
            # Fetch a URL
            if len(args_parts) < 2:
                return "Error: No URL specified."

            url = args_parts[1]

            try:
                content, metadata = self.web_tool.fetch_url(url)
                return f"Content from {url}:\n\nTitle: {metadata.get('title', 'N/A')}\n\n{content[:2000]}..."
            except Exception as e:
                return f"Error fetching URL: {e}"

        else:
            return f"Error: Unknown web operation: {operation}"

    def _execute_codebase(self, args: str) -> str:
        """Execute a codebase operation.

        Args:
            args: The arguments for the codebase operation.

        Returns:
            The result of the operation.
        """
        # Parse the arguments
        args_parts = args.strip().split(maxsplit=1)
        if not args_parts:
            return "Error: No codebase operation specified."

        operation = args_parts[0]

        if operation == "find":
            # Find files
            pattern = args_parts[1] if len(args_parts) > 1 else "*"

            try:
                files = self.codebase_tool.find_files(pattern)
                return f"Files matching {pattern}:\n\n{json.dumps(files, indent=2)}"
            except Exception as e:
                return f"Error finding files: {e}"

        elif operation == "find_code":
            # Find code files
            try:
                files = self.codebase_tool.find_code_files()
                return f"Code files found:\n\n{json.dumps(files, indent=2)}"
            except Exception as e:
                return f"Error finding code files: {e}"

        elif operation == "search":
            # Search for a pattern in code files
            if len(args_parts) < 2:
                return "Error: No search pattern specified."

            # Parse the pattern and file pattern
            search_parts = args_parts[1].split(maxsplit=1)
            if len(search_parts) < 2:
                pattern = search_parts[0]
                file_pattern = "*"
            else:
                pattern = search_parts[0]
                file_pattern = search_parts[1]

            try:
                results = self.codebase_tool.search_code(pattern, file_pattern)
                return f"Search results for {pattern} in {file_pattern}:\n\n{json.dumps(results, indent=2)}"
            except Exception as e:
                return f"Error searching code: {e}"

        elif operation == "analyze":
            # Analyze a code file
            if len(args_parts) < 2:
                return "Error: No file path specified."

            file_path = args_parts[1]

            try:
                results = self.codebase_tool.analyze_file(file_path)
                return f"Analysis results for {file_path}:\n\n{json.dumps(results, indent=2)}"
            except Exception as e:
                return f"Error analyzing file: {e}"

        else:
            return f"Error: Unknown codebase operation: {operation}"

    def _execute_vision(self, args: str) -> str:
        """Execute a vision operation.

        Args:
            args: The arguments for the vision operation.

        Returns:
            The result of the operation.
        """
        # Parse the arguments
        args_parts = args.strip().split(maxsplit=1)
        if not args_parts:
            return "Error: No vision operation specified."

        operation = args_parts[0]

        if operation == "take_screenshot":
            # Take a screenshot
            path = args_parts[1] if len(args_parts) > 1 else None

            try:
                screenshot_path = self.vision_tool.take_screenshot(path)
                return f"Screenshot taken and saved to: {screenshot_path}"
            except Exception as e:
                return f"Error taking screenshot: {e}"

        elif operation == "load_image":
            # Load an image
            if len(args_parts) < 2:
                return "Error: No image path specified."

            image_path = args_parts[1]

            try:
                image = self.vision_tool.load_image(image_path)
                return f"Image loaded: {image_path} (Size: {image.width}x{image.height})"
            except Exception as e:
                return f"Error loading image: {e}"

        else:
            return f"Error: Unknown vision operation: {operation}"


    def _execute_patch(self, args: str) -> str:
        """Execute a patch operation.

        Args:
            args: The arguments for the patch operation.

        Returns:
            The result of the operation.
        """
        # Parse the arguments
        args_parts = args.strip().split(maxsplit=1)
        if not args_parts:
            return "Error: No patch operation specified."

        operation = args_parts[0]

        if operation == "apply":
            # Apply a patch
            if len(args_parts) < 2:
                return "Error: No patch arguments specified."

            # Parse the file path, original code, and updated code
            try:
                patch_parts = args_parts[1].split(maxsplit=2)
                if len(patch_parts) < 3:
                    return "Error: Insufficient patch arguments. Need file_path, original_code, and updated_code."

                file_path = patch_parts[0]
                original_code = patch_parts[1]
                updated_code = patch_parts[2]

                # Apply the patch
                result = self.patch_tool.apply_patch(file_path, original_code, updated_code)
                return f"Patch applied successfully to {file_path}"
            except Exception as e:
                return f"Error applying patch: {e}"

        else:
            return f"Error: Unknown patch operation: {operation}"
