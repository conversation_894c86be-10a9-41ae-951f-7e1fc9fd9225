"""
Advanced Tools for Enhanced AI Agent
Includes 20+ powerful coding tools with comprehensive capabilities.
"""

import os
import re
import json
import time
import subprocess
import threading
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass
import shutil
import tempfile
import zipfile
import tarfile
from urllib.parse import urlparse
import hashlib

from .tool_manager import Tool
from utils import get_logger

logger = get_logger()

@dataclass
class SearchResult:
    """Search result with context."""
    file_path: str
    line_number: int
    content: str
    context_before: List[str]
    context_after: List[str]
    match_start: int
    match_end: int

class AdvancedFileManager:
    """Advanced file management with enhanced capabilities."""
    
    def __init__(self, workspace_dir: Path):
        self.workspace_dir = workspace_dir
        self.lock = threading.RLock()
    
    def advanced_grep(self, pattern: str, file_pattern: str = "*", 
                     context_lines: int = 3, case_sensitive: bool = False,
                     include_binary: bool = False) -> List[SearchResult]:
        """Advanced grep with context and options."""
        results = []
        flags = 0 if case_sensitive else re.IGNORECASE
        regex = re.compile(pattern, flags)
        
        try:
            for file_path in self._find_files(file_pattern):
                if not include_binary and self._is_binary_file(file_path):
                    continue
                
                try:
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        lines = f.readlines()
                    
                    for i, line in enumerate(lines):
                        match = regex.search(line)
                        if match:
                            context_before = lines[max(0, i-context_lines):i]
                            context_after = lines[i+1:i+1+context_lines]
                            
                            result = SearchResult(
                                file_path=str(file_path),
                                line_number=i + 1,
                                content=line.rstrip(),
                                context_before=[l.rstrip() for l in context_before],
                                context_after=[l.rstrip() for l in context_after],
                                match_start=match.start(),
                                match_end=match.end()
                            )
                            results.append(result)
                            
                except Exception as e:
                    logger.warning(f"Error processing file {file_path}: {e}")
                    
        except Exception as e:
            logger.error(f"Error in advanced grep: {e}")
            
        return results
    
    def _find_files(self, pattern: str) -> List[Path]:
        """Find files matching pattern."""
        files = []
        try:
            if pattern == "*":
                for root, dirs, filenames in os.walk(self.workspace_dir):
                    for filename in filenames:
                        files.append(Path(root) / filename)
            else:
                files = list(self.workspace_dir.rglob(pattern))
        except Exception as e:
            logger.error(f"Error finding files: {e}")
        return files
    
    def _is_binary_file(self, file_path: Path) -> bool:
        """Check if file is binary."""
        try:
            with open(file_path, 'rb') as f:
                chunk = f.read(1024)
                return b'\0' in chunk
        except:
            return True
    
    def create_backup(self, file_path: str, backup_dir: Optional[str] = None) -> str:
        """Create backup of a file."""
        try:
            source_path = Path(self.workspace_dir) / file_path
            if not source_path.exists():
                raise FileNotFoundError(f"File not found: {file_path}")
            
            if backup_dir:
                backup_path = Path(backup_dir)
            else:
                backup_path = self.workspace_dir / ".backups"
            
            backup_path.mkdir(parents=True, exist_ok=True)
            
            timestamp = int(time.time())
            backup_filename = f"{source_path.name}.backup.{timestamp}"
            backup_file = backup_path / backup_filename
            
            shutil.copy2(source_path, backup_file)
            return str(backup_file)
            
        except Exception as e:
            logger.error(f"Error creating backup: {e}")
            raise

    def batch_rename(self, pattern: str, replacement: str, file_pattern: str = "*") -> List[Tuple[str, str]]:
        """Batch rename files using regex pattern."""
        renamed_files = []
        regex = re.compile(pattern)
        
        try:
            for file_path in self._find_files(file_pattern):
                old_name = file_path.name
                new_name = regex.sub(replacement, old_name)
                
                if new_name != old_name:
                    new_path = file_path.parent / new_name
                    file_path.rename(new_path)
                    renamed_files.append((str(file_path), str(new_path)))
                    
        except Exception as e:
            logger.error(f"Error in batch rename: {e}")
            
        return renamed_files

    def calculate_checksums(self, file_pattern: str = "*") -> Dict[str, str]:
        """Calculate MD5 checksums for files."""
        checksums = {}
        
        try:
            for file_path in self._find_files(file_pattern):
                if file_path.is_file():
                    with open(file_path, 'rb') as f:
                        content = f.read()
                        checksum = hashlib.md5(content).hexdigest()
                        checksums[str(file_path)] = checksum
                        
        except Exception as e:
            logger.error(f"Error calculating checksums: {e}")
            
        return checksums

class AdvancedTerminalManager:
    """Advanced terminal management with session support."""
    
    def __init__(self, workspace_dir: Path):
        self.workspace_dir = workspace_dir
        self.sessions = {}
        self.lock = threading.RLock()
    
    def create_session(self, session_id: str, shell: str = "bash") -> bool:
        """Create a persistent terminal session."""
        try:
            with self.lock:
                if session_id in self.sessions:
                    return False
                
                # Create a new subprocess for the session
                process = subprocess.Popen(
                    shell,
                    stdin=subprocess.PIPE,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    cwd=self.workspace_dir,
                    bufsize=0
                )
                
                self.sessions[session_id] = {
                    'process': process,
                    'history': [],
                    'created_at': time.time()
                }
                
                return True
                
        except Exception as e:
            logger.error(f"Error creating terminal session: {e}")
            return False
    
    def execute_in_session(self, session_id: str, command: str, timeout: float = 30.0) -> Tuple[str, str, int]:
        """Execute command in a specific session."""
        try:
            with self.lock:
                if session_id not in self.sessions:
                    raise ValueError(f"Session {session_id} not found")
                
                session = self.sessions[session_id]
                process = session['process']
                
                # Send command
                process.stdin.write(f"{command}\n")
                process.stdin.flush()
                
                # Read output with timeout
                stdout_lines = []
                stderr_lines = []
                
                start_time = time.time()
                while time.time() - start_time < timeout:
                    if process.stdout.readable():
                        line = process.stdout.readline()
                        if line:
                            stdout_lines.append(line)
                    
                    if process.stderr.readable():
                        line = process.stderr.readline()
                        if line:
                            stderr_lines.append(line)
                    
                    if process.poll() is not None:
                        break
                    
                    time.sleep(0.1)
                
                stdout = ''.join(stdout_lines)
                stderr = ''.join(stderr_lines)
                return_code = process.returncode or 0
                
                # Update session history
                session['history'].append({
                    'command': command,
                    'stdout': stdout,
                    'stderr': stderr,
                    'return_code': return_code,
                    'timestamp': time.time()
                })
                
                return stdout, stderr, return_code
                
        except Exception as e:
            logger.error(f"Error executing command in session: {e}")
            return "", str(e), 1
    
    def get_session_history(self, session_id: str) -> List[Dict[str, Any]]:
        """Get command history for a session."""
        with self.lock:
            if session_id in self.sessions:
                return self.sessions[session_id]['history'].copy()
            return []
    
    def close_session(self, session_id: str) -> bool:
        """Close a terminal session."""
        try:
            with self.lock:
                if session_id in self.sessions:
                    process = self.sessions[session_id]['process']
                    process.terminate()
                    process.wait(timeout=5)
                    del self.sessions[session_id]
                    return True
                return False
                
        except Exception as e:
            logger.error(f"Error closing session: {e}")
            return False

class AdvancedProjectManager:
    """Advanced project management and scaffolding."""
    
    def __init__(self, workspace_dir: Path):
        self.workspace_dir = workspace_dir
        self.templates = {
            'python': self._python_template,
            'javascript': self._javascript_template,
            'react': self._react_template,
            'flask': self._flask_template,
            'fastapi': self._fastapi_template,
            'django': self._django_template,
            'nodejs': self._nodejs_template,
            'vue': self._vue_template,
            'angular': self._angular_template,
            'rust': self._rust_template,
            'go': self._go_template,
            'java': self._java_template,
            'cpp': self._cpp_template,
            'csharp': self._csharp_template
        }
    
    def create_project(self, project_name: str, template: str, options: Dict[str, Any] = None) -> Dict[str, Any]:
        """Create a new project from template."""
        try:
            if template not in self.templates:
                raise ValueError(f"Unknown template: {template}")
            
            project_path = self.workspace_dir / project_name
            if project_path.exists():
                raise ValueError(f"Project {project_name} already exists")
            
            project_path.mkdir(parents=True)
            
            # Generate project structure
            template_func = self.templates[template]
            structure = template_func(project_name, options or {})
            
            # Create files and directories
            created_files = []
            for item in structure:
                if item['type'] == 'directory':
                    dir_path = project_path / item['path']
                    dir_path.mkdir(parents=True, exist_ok=True)
                elif item['type'] == 'file':
                    file_path = project_path / item['path']
                    file_path.parent.mkdir(parents=True, exist_ok=True)
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(item['content'])
                    created_files.append(str(file_path))
            
            return {
                'project_name': project_name,
                'template': template,
                'project_path': str(project_path),
                'created_files': created_files,
                'success': True
            }
            
        except Exception as e:
            logger.error(f"Error creating project: {e}")
            return {
                'project_name': project_name,
                'template': template,
                'error': str(e),
                'success': False
            }
    
    def _python_template(self, name: str, options: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Python project template."""
        return [
            {'type': 'directory', 'path': name},
            {'type': 'directory', 'path': 'tests'},
            {'type': 'directory', 'path': 'docs'},
            {'type': 'file', 'path': 'main.py', 'content': f'#!/usr/bin/env python3\n"""\n{name} - Main module\n"""\n\ndef main():\n    print("Hello from {name}!")\n\nif __name__ == "__main__":\n    main()\n'},
            {'type': 'file', 'path': f'{name}/__init__.py', 'content': f'"""{name} package"""\n\n__version__ = "0.1.0"\n'},
            {'type': 'file', 'path': 'requirements.txt', 'content': '# Add your dependencies here\n'},
            {'type': 'file', 'path': 'README.md', 'content': f'# {name}\n\nDescription of your project.\n\n## Installation\n\n```bash\npip install -r requirements.txt\n```\n\n## Usage\n\n```bash\npython main.py\n```\n'},
            {'type': 'file', 'path': 'setup.py', 'content': f'from setuptools import setup, find_packages\n\nsetup(\n    name="{name}",\n    version="0.1.0",\n    packages=find_packages(),\n    install_requires=[],\n    author="Your Name",\n    description="A Python project",\n    python_requires=">=3.7",\n)\n'},
            {'type': 'file', 'path': 'tests/test_{name}.py', 'content': f'import unittest\nfrom {name} import main\n\nclass Test{name.capitalize()}(unittest.TestCase):\n    def test_main(self):\n        # Add your tests here\n        pass\n\nif __name__ == "__main__":\n    unittest.main()\n'}
        ]
    
    def _javascript_template(self, name: str, options: Dict[str, Any]) -> List[Dict[str, Any]]:
        """JavaScript project template."""
        return [
            {'type': 'file', 'path': 'package.json', 'content': f'{{\n  "name": "{name}",\n  "version": "1.0.0",\n  "description": "",\n  "main": "index.js",\n  "scripts": {{\n    "start": "node index.js",\n    "test": "echo \\"Error: no test specified\\" && exit 1"\n  }},\n  "keywords": [],\n  "author": "",\n  "license": "ISC"\n}}'},
            {'type': 'file', 'path': 'index.js', 'content': f'// {name} - Main entry point\n\nconsole.log("Hello from {name}!");\n\n// Add your code here\n'},
            {'type': 'file', 'path': 'README.md', 'content': f'# {name}\n\nDescription of your JavaScript project.\n\n## Installation\n\n```bash\nnpm install\n```\n\n## Usage\n\n```bash\nnpm start\n```\n'},
            {'type': 'file', 'path': '.gitignore', 'content': 'node_modules/\n*.log\n.env\ndist/\nbuild/\n'}
        ]

    def _react_template(self, name: str, options: Dict[str, Any]) -> List[Dict[str, Any]]:
        """React project template."""
        return [
            {'type': 'file', 'path': 'package.json', 'content': f'{{\n  "name": "{name}",\n  "version": "0.1.0",\n  "private": true,\n  "dependencies": {{\n    "react": "^18.2.0",\n    "react-dom": "^18.2.0",\n    "react-scripts": "5.0.1"\n  }},\n  "scripts": {{\n    "start": "react-scripts start",\n    "build": "react-scripts build",\n    "test": "react-scripts test",\n    "eject": "react-scripts eject"\n  }},\n  "browserslist": {{\n    "production": [\n      ">0.2%",\n      "not dead",\n      "not op_mini all"\n    ],\n    "development": [\n      "last 1 chrome version",\n      "last 1 firefox version",\n      "last 1 safari version"\n    ]\n  }}\n}}'},
            {'type': 'directory', 'path': 'public'},
            {'type': 'directory', 'path': 'src'},
            {'type': 'file', 'path': 'public/index.html', 'content': f'<!DOCTYPE html>\n<html lang="en">\n<head>\n    <meta charset="utf-8" />\n    <meta name="viewport" content="width=device-width, initial-scale=1" />\n    <title>{name}</title>\n</head>\n<body>\n    <noscript>You need to enable JavaScript to run this app.</noscript>\n    <div id="root"></div>\n</body>\n</html>'},
            {'type': 'file', 'path': 'src/index.js', 'content': 'import React from "react";\nimport ReactDOM from "react-dom/client";\nimport App from "./App";\n\nconst root = ReactDOM.createRoot(document.getElementById("root"));\nroot.render(<App />);'},
            {'type': 'file', 'path': 'src/App.js', 'content': f'import React from "react";\n\nfunction App() {{\n  return (\n    <div className="App">\n      <header className="App-header">\n        <h1>Welcome to {name}</h1>\n        <p>Edit src/App.js and save to reload.</p>\n      </header>\n    </div>\n  );\n}}\n\nexport default App;'},
            {'type': 'file', 'path': 'README.md', 'content': f'# {name}\n\nThis project was bootstrapped with Create React App.\n\n## Available Scripts\n\n### `npm start`\n\nRuns the app in development mode.\n\n### `npm test`\n\nLaunches the test runner.\n\n### `npm run build`\n\nBuilds the app for production.\n'}
        ]

    def _flask_template(self, name: str, options: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Flask project template."""
        return [
            {'type': 'file', 'path': 'app.py', 'content': f'from flask import Flask, render_template, jsonify\n\napp = Flask(__name__)\n\<EMAIL>("/")\ndef index():\n    return render_template("index.html", title="{name}")\n\<EMAIL>("/api/health")\ndef health():\n    return jsonify({{"status": "healthy", "service": "{name}"}})\n\nif __name__ == "__main__":\n    app.run(debug=True)'},
            {'type': 'directory', 'path': 'templates'},
            {'type': 'directory', 'path': 'static'},
            {'type': 'directory', 'path': 'static/css'},
            {'type': 'directory', 'path': 'static/js'},
            {'type': 'file', 'path': 'templates/index.html', 'content': f'<!DOCTYPE html>\n<html>\n<head>\n    <title>{{{{ title }}}}</title>\n    <link rel="stylesheet" href="{{{{ url_for(\'static\', filename=\'css/style.css\') }}}}"\n</head>\n<body>\n    <h1>Welcome to {name}</h1>\n    <p>Your Flask application is running!</p>\n</body>\n</html>'},
            {'type': 'file', 'path': 'static/css/style.css', 'content': 'body {\n    font-family: Arial, sans-serif;\n    margin: 40px;\n    background-color: #f5f5f5;\n}\n\nh1 {\n    color: #333;\n    text-align: center;\n}'},
            {'type': 'file', 'path': 'requirements.txt', 'content': 'Flask==2.3.3\nWerkzeug==2.3.7'},
            {'type': 'file', 'path': 'README.md', 'content': f'# {name}\n\nA Flask web application.\n\n## Installation\n\n```bash\npip install -r requirements.txt\n```\n\n## Usage\n\n```bash\npython app.py\n```\n\nVisit http://localhost:5000 to see your application.'}
        ]

    def _fastapi_template(self, name: str, options: Dict[str, Any]) -> List[Dict[str, Any]]:
        """FastAPI project template."""
        return [
            {'type': 'file', 'path': 'main.py', 'content': f'from fastapi import FastAPI\nfrom pydantic import BaseModel\n\napp = FastAPI(title="{name}", version="1.0.0")\n\nclass HealthResponse(BaseModel):\n    status: str\n    service: str\n\<EMAIL>("/")\nasync def root():\n    return {{"message": "Welcome to {name}"}}\n\<EMAIL>("/health", response_model=HealthResponse)\nasync def health():\n    return HealthResponse(status="healthy", service="{name}")\n\nif __name__ == "__main__":\n    import uvicorn\n    uvicorn.run(app, host="0.0.0.0", port=8000)'},
            {'type': 'file', 'path': 'requirements.txt', 'content': 'fastapi==0.104.1\nuvicorn[standard]==0.24.0\npydantic==2.5.0'},
            {'type': 'file', 'path': 'README.md', 'content': f'# {name}\n\nA FastAPI application.\n\n## Installation\n\n```bash\npip install -r requirements.txt\n```\n\n## Usage\n\n```bash\npython main.py\n```\n\nOr with uvicorn:\n\n```bash\nuvicorn main:app --reload\n```\n\nVisit http://localhost:8000/docs for the interactive API documentation.'}
        ]

    def _nodejs_template(self, name: str, options: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Node.js project template."""
        return [
            {'type': 'file', 'path': 'package.json', 'content': f'{{\n  "name": "{name}",\n  "version": "1.0.0",\n  "description": "A Node.js application",\n  "main": "server.js",\n  "scripts": {{\n    "start": "node server.js",\n    "dev": "nodemon server.js",\n    "test": "jest"\n  }},\n  "dependencies": {{\n    "express": "^4.18.2",\n    "cors": "^2.8.5",\n    "helmet": "^7.1.0"\n  }},\n  "devDependencies": {{\n    "nodemon": "^3.0.1",\n    "jest": "^29.7.0"\n  }}\n}}'},
            {'type': 'file', 'path': 'server.js', 'content': f'const express = require("express");\nconst cors = require("cors");\nconst helmet = require("helmet");\n\nconst app = express();\nconst PORT = process.env.PORT || 3000;\n\n// Middleware\napp.use(helmet());\napp.use(cors());\napp.use(express.json());\n\n// Routes\napp.get("/", (req, res) => {{\n  res.json({{\n    message: "Welcome to {name}",\n    version: "1.0.0"\n  }});\n}});\n\napp.get("/health", (req, res) => {{\n  res.json({{\n    status: "healthy",\n    service: "{name}",\n    timestamp: new Date().toISOString()\n  }});\n}});\n\n// Start server\napp.listen(PORT, () => {{\n  console.log(`{name} server running on port ${{PORT}}`);\n}});'},
            {'type': 'file', 'path': 'README.md', 'content': f'# {name}\n\nA Node.js Express application.\n\n## Installation\n\n```bash\nnpm install\n```\n\n## Usage\n\n```bash\nnpm start\n```\n\nFor development with auto-reload:\n\n```bash\nnpm run dev\n```\n\n## Testing\n\n```bash\nnpm test\n```'}
        ]

    def _rust_template(self, name: str, options: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Rust project template."""
        return [
            {'type': 'file', 'path': 'Cargo.toml', 'content': f'[package]\nname = "{name}"\nversion = "0.1.0"\nedition = "2021"\n\n[dependencies]\n'},
            {'type': 'directory', 'path': 'src'},
            {'type': 'file', 'path': 'src/main.rs', 'content': f'fn main() {{\n    println!("Hello from {name}!");\n}}'},
            {'type': 'file', 'path': 'README.md', 'content': f'# {name}\n\nA Rust application.\n\n## Build\n\n```bash\ncargo build\n```\n\n## Run\n\n```bash\ncargo run\n```\n\n## Test\n\n```bash\ncargo test\n```'}
        ]

    def _go_template(self, name: str, options: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Go project template."""
        return [
            {'type': 'file', 'path': 'go.mod', 'content': f'module {name}\n\ngo 1.21\n'},
            {'type': 'file', 'path': 'main.go', 'content': f'package main\n\nimport "fmt"\n\nfunc main() {{\n\tfmt.Println("Hello from {name}!")\n}}'},
            {'type': 'file', 'path': 'README.md', 'content': f'# {name}\n\nA Go application.\n\n## Build\n\n```bash\ngo build\n```\n\n## Run\n\n```bash\ngo run main.go\n```\n\n## Test\n\n```bash\ngo test\n```'}
        ]

    # Placeholder methods for other templates
    def _django_template(self, name: str, options: Dict[str, Any]) -> List[Dict[str, Any]]:
        return [{'type': 'file', 'path': 'README.md', 'content': f'# {name}\n\nDjango project template - implement as needed.'}]

    def _vue_template(self, name: str, options: Dict[str, Any]) -> List[Dict[str, Any]]:
        return [{'type': 'file', 'path': 'README.md', 'content': f'# {name}\n\nVue.js project template - implement as needed.'}]

    def _angular_template(self, name: str, options: Dict[str, Any]) -> List[Dict[str, Any]]:
        return [{'type': 'file', 'path': 'README.md', 'content': f'# {name}\n\nAngular project template - implement as needed.'}]

    def _java_template(self, name: str, options: Dict[str, Any]) -> List[Dict[str, Any]]:
        return [{'type': 'file', 'path': 'README.md', 'content': f'# {name}\n\nJava project template - implement as needed.'}]

    def _cpp_template(self, name: str, options: Dict[str, Any]) -> List[Dict[str, Any]]:
        return [{'type': 'file', 'path': 'README.md', 'content': f'# {name}\n\nC++ project template - implement as needed.'}]

    def _csharp_template(self, name: str, options: Dict[str, Any]) -> List[Dict[str, Any]]:
        return [{'type': 'file', 'path': 'README.md', 'content': f'# {name}\n\nC# project template - implement as needed.'}]

class AdvancedCodeAnalyzer:
    """Advanced code analysis and metrics."""

    def __init__(self, workspace_dir: Path):
        self.workspace_dir = workspace_dir

    def analyze_complexity(self, file_path: str) -> Dict[str, Any]:
        """Analyze code complexity metrics."""
        try:
            full_path = Path(self.workspace_dir) / file_path
            if not full_path.exists():
                raise FileNotFoundError(f"File not found: {file_path}")

            with open(full_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Basic complexity analysis
            lines = content.split('\n')
            total_lines = len(lines)
            code_lines = len([line for line in lines if line.strip() and not line.strip().startswith('#')])
            comment_lines = len([line for line in lines if line.strip().startswith('#')])
            blank_lines = total_lines - code_lines - comment_lines

            # Count functions/methods
            function_count = len(re.findall(r'def\s+\w+', content))
            class_count = len(re.findall(r'class\s+\w+', content))

            # Cyclomatic complexity (basic estimation)
            complexity_keywords = ['if', 'elif', 'else', 'for', 'while', 'try', 'except', 'with']
            complexity_score = sum(len(re.findall(rf'\b{keyword}\b', content)) for keyword in complexity_keywords)

            return {
                'file_path': file_path,
                'total_lines': total_lines,
                'code_lines': code_lines,
                'comment_lines': comment_lines,
                'blank_lines': blank_lines,
                'function_count': function_count,
                'class_count': class_count,
                'complexity_score': complexity_score,
                'comment_ratio': comment_lines / total_lines if total_lines > 0 else 0
            }

        except Exception as e:
            logger.error(f"Error analyzing complexity: {e}")
            return {'error': str(e)}

    def find_code_smells(self, file_path: str) -> List[Dict[str, Any]]:
        """Find potential code smells."""
        smells = []

        try:
            full_path = Path(self.workspace_dir) / file_path
            with open(full_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            for i, line in enumerate(lines, 1):
                line_stripped = line.strip()

                # Long lines
                if len(line) > 120:
                    smells.append({
                        'type': 'long_line',
                        'line': i,
                        'message': f'Line too long ({len(line)} characters)',
                        'severity': 'warning'
                    })

                # TODO comments
                if 'TODO' in line_stripped or 'FIXME' in line_stripped:
                    smells.append({
                        'type': 'todo',
                        'line': i,
                        'message': 'TODO/FIXME comment found',
                        'severity': 'info'
                    })

                # Hardcoded strings that might be secrets
                if re.search(r'(password|secret|key|token)\s*=\s*["\'][^"\']+["\']', line_stripped, re.IGNORECASE):
                    smells.append({
                        'type': 'potential_secret',
                        'line': i,
                        'message': 'Potential hardcoded secret',
                        'severity': 'error'
                    })

                # Empty except blocks
                if line_stripped == 'except:' or line_stripped.startswith('except '):
                    next_line = lines[i] if i < len(lines) else ''
                    if next_line.strip() == 'pass':
                        smells.append({
                            'type': 'empty_except',
                            'line': i,
                            'message': 'Empty except block',
                            'severity': 'warning'
                        })

            return smells

        except Exception as e:
            logger.error(f"Error finding code smells: {e}")
            return [{'error': str(e)}]

class AdvancedTestManager:
    """Advanced testing capabilities."""

    def __init__(self, workspace_dir: Path):
        self.workspace_dir = workspace_dir

    def generate_test_template(self, file_path: str, test_framework: str = 'unittest') -> str:
        """Generate test template for a given file."""
        try:
            full_path = Path(self.workspace_dir) / file_path
            if not full_path.exists():
                raise FileNotFoundError(f"File not found: {file_path}")

            with open(full_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Extract functions and classes
            functions = re.findall(r'def\s+(\w+)', content)
            classes = re.findall(r'class\s+(\w+)', content)

            module_name = full_path.stem

            if test_framework == 'unittest':
                template = f'''import unittest
from {module_name} import {', '.join(classes + functions) if classes + functions else '*'}

class Test{module_name.capitalize()}(unittest.TestCase):

    def setUp(self):
        """Set up test fixtures before each test method."""
        pass

    def tearDown(self):
        """Clean up after each test method."""
        pass
'''

                for func in functions:
                    if not func.startswith('_'):  # Skip private functions
                        template += f'''
    def test_{func}(self):
        """Test {func} function."""
        # TODO: Implement test for {func}
        self.fail("Test not implemented")
'''

                for cls in classes:
                    template += f'''
    def test_{cls.lower()}_creation(self):
        """Test {cls} class creation."""
        # TODO: Implement test for {cls}
        self.fail("Test not implemented")
'''

                template += '''
if __name__ == '__main__':
    unittest.main()
'''

            elif test_framework == 'pytest':
                template = f'''import pytest
from {module_name} import {', '.join(classes + functions) if classes + functions else '*'}

@pytest.fixture
def setup_data():
    """Fixture to set up test data."""
    return {{}}
'''

                for func in functions:
                    if not func.startswith('_'):
                        template += f'''
def test_{func}(setup_data):
    """Test {func} function."""
    # TODO: Implement test for {func}
    assert False, "Test not implemented"
'''

                for cls in classes:
                    template += f'''
def test_{cls.lower()}_creation(setup_data):
    """Test {cls} class creation."""
    # TODO: Implement test for {cls}
    assert False, "Test not implemented"
'''

            else:
                raise ValueError(f"Unsupported test framework: {test_framework}")

            return template

        except Exception as e:
            logger.error(f"Error generating test template: {e}")
            return f"# Error generating test template: {e}"

    def run_tests(self, test_pattern: str = "test_*.py", framework: str = "unittest") -> Dict[str, Any]:
        """Run tests and return results."""
        try:
            if framework == "unittest":
                cmd = f"python -m unittest discover -s . -p '{test_pattern}' -v"
            elif framework == "pytest":
                cmd = f"python -m pytest {test_pattern} -v"
            else:
                raise ValueError(f"Unsupported test framework: {framework}")

            process = subprocess.run(
                cmd,
                shell=True,
                cwd=self.workspace_dir,
                capture_output=True,
                text=True,
                timeout=300
            )

            return {
                'success': process.returncode == 0,
                'stdout': process.stdout,
                'stderr': process.stderr,
                'return_code': process.returncode,
                'framework': framework
            }

        except Exception as e:
            logger.error(f"Error running tests: {e}")
            return {
                'success': False,
                'error': str(e),
                'framework': framework
            }

class AdvancedDeploymentManager:
    """Advanced deployment and DevOps capabilities."""

    def __init__(self, workspace_dir: Path):
        self.workspace_dir = workspace_dir

    def generate_dockerfile(self, project_type: str, options: Dict[str, Any] = None) -> str:
        """Generate Dockerfile for different project types."""
        options = options or {}

        dockerfiles = {
            'python': f'''FROM python:3.11-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE {options.get('port', 8000)}

CMD ["python", "{options.get('main_file', 'main.py')}"]
''',
            'nodejs': f'''FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .

EXPOSE {options.get('port', 3000)}

CMD ["npm", "start"]
''',
            'react': '''FROM node:18-alpine as build

WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=build /app/build /usr/share/nginx/html
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
''',
            'go': f'''FROM golang:1.21-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN go build -o main .

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/

COPY --from=builder /app/main .

EXPOSE {options.get('port', 8080)}

CMD ["./main"]
'''
        }

        return dockerfiles.get(project_type, f'# Dockerfile for {project_type} - template not available')

    def generate_docker_compose(self, services: List[Dict[str, Any]]) -> str:
        """Generate docker-compose.yml file."""
        compose_content = "version: '3.8'\n\nservices:\n"

        for service in services:
            name = service.get('name', 'app')
            compose_content += f"  {name}:\n"

            if 'build' in service:
                compose_content += f"    build: {service['build']}\n"
            elif 'image' in service:
                compose_content += f"    image: {service['image']}\n"

            if 'ports' in service:
                compose_content += "    ports:\n"
                for port in service['ports']:
                    compose_content += f"      - \"{port}\"\n"

            if 'environment' in service:
                compose_content += "    environment:\n"
                for key, value in service['environment'].items():
                    compose_content += f"      - {key}={value}\n"

            if 'volumes' in service:
                compose_content += "    volumes:\n"
                for volume in service['volumes']:
                    compose_content += f"      - {volume}\n"

            if 'depends_on' in service:
                compose_content += "    depends_on:\n"
                for dep in service['depends_on']:
                    compose_content += f"      - {dep}\n"

            compose_content += "\n"

        return compose_content

    def generate_github_actions(self, project_type: str, options: Dict[str, Any] = None) -> str:
        """Generate GitHub Actions workflow."""
        options = options or {}

        workflows = {
            'python': f'''name: Python CI/CD

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.8, 3.9, 3.10, 3.11]

    steps:
    - uses: actions/checkout@v3

    - name: Set up Python ${{{{ matrix.python-version }}}}
      uses: actions/setup-python@v4
      with:
        python-version: ${{{{ matrix.python-version }}}}

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-cov

    - name: Run tests
      run: |
        pytest --cov=. --cov-report=xml

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v3

    - name: Deploy to production
      run: |
        echo "Deploy to production"
        # Add your deployment steps here
''',
            'nodejs': '''name: Node.js CI/CD

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [16.x, 18.x, 20.x]

    steps:
    - uses: actions/checkout@v3

    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'

    - run: npm ci
    - run: npm run build --if-present
    - run: npm test

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v3

    - name: Deploy to production
      run: |
        echo "Deploy to production"
        # Add your deployment steps here
'''
        }

        return workflows.get(project_type, f'# GitHub Actions workflow for {project_type} - template not available')
