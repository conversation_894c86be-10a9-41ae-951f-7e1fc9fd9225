#!/usr/bin/env python3
"""
Comprehensive Test Runner for Enhanced AI Agent
Runs all tests and provides detailed reporting.
"""

import unittest
import sys
import time
import json
from pathlib import Path
from io import StringIO

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from tests import TEST_CONFIG


class TestResult:
    """Enhanced test result tracking."""
    
    def __init__(self):
        self.start_time = time.time()
        self.end_time = None
        self.total_tests = 0
        self.passed_tests = 0
        self.failed_tests = 0
        self.error_tests = 0
        self.skipped_tests = 0
        self.test_results = []
        self.coverage_data = {}
    
    def add_test_result(self, test_name, status, duration, error_msg=None):
        """Add individual test result."""
        self.test_results.append({
            'test_name': test_name,
            'status': status,
            'duration': duration,
            'error_msg': error_msg
        })
        
        self.total_tests += 1
        if status == 'PASS':
            self.passed_tests += 1
        elif status == 'FAIL':
            self.failed_tests += 1
        elif status == 'ERROR':
            self.error_tests += 1
        elif status == 'SKIP':
            self.skipped_tests += 1
    
    def finalize(self):
        """Finalize test results."""
        self.end_time = time.time()
    
    def get_summary(self):
        """Get test summary."""
        duration = self.end_time - self.start_time if self.end_time else 0
        success_rate = (self.passed_tests / self.total_tests * 100) if self.total_tests > 0 else 0
        
        return {
            'total_tests': self.total_tests,
            'passed': self.passed_tests,
            'failed': self.failed_tests,
            'errors': self.error_tests,
            'skipped': self.skipped_tests,
            'success_rate': round(success_rate, 2),
            'duration': round(duration, 2),
            'test_results': self.test_results
        }


class EnhancedTestRunner:
    """Enhanced test runner with detailed reporting."""
    
    def __init__(self, verbosity=2):
        self.verbosity = verbosity
        self.result = TestResult()
    
    def discover_and_run_tests(self):
        """Discover and run all tests."""
        print("🚀 Enhanced AI Agent - Comprehensive Test Suite")
        print("=" * 60)
        
        # Test modules to run
        test_modules = [
            'tests.test_enhanced_agent',
            'tests.test_tools',
            'tests.test_workflow_manager'
        ]
        
        for module_name in test_modules:
            print(f"\n📋 Running tests from {module_name}")
            print("-" * 40)
            
            try:
                # Import and run tests from module
                module = __import__(module_name, fromlist=[''])
                self._run_module_tests(module, module_name)
            except ImportError as e:
                print(f"❌ Could not import {module_name}: {e}")
                self.result.add_test_result(module_name, 'ERROR', 0, str(e))
            except Exception as e:
                print(f"❌ Error running {module_name}: {e}")
                self.result.add_test_result(module_name, 'ERROR', 0, str(e))
        
        self.result.finalize()
        self._print_final_summary()
        return self.result
    
    def _run_module_tests(self, module, module_name):
        """Run tests from a specific module."""
        # Create test suite from module
        loader = unittest.TestLoader()
        suite = loader.loadTestsFromModule(module)
        
        # Run tests with custom result handler
        stream = StringIO()
        runner = unittest.TextTestRunner(stream=stream, verbosity=self.verbosity)
        
        start_time = time.time()
        test_result = runner.run(suite)
        duration = time.time() - start_time
        
        # Process results
        self._process_test_result(test_result, module_name, duration)
        
        # Print module summary
        print(f"✅ {module_name}: {test_result.testsRun} tests, "
              f"{len(test_result.failures)} failures, "
              f"{len(test_result.errors)} errors "
              f"({duration:.2f}s)")
    
    def _process_test_result(self, test_result, module_name, duration):
        """Process unittest results."""
        # Add successful tests
        successful_tests = (test_result.testsRun - 
                          len(test_result.failures) - 
                          len(test_result.errors) - 
                          len(test_result.skipped))
        
        for _ in range(successful_tests):
            self.result.add_test_result(f"{module_name}.test", 'PASS', duration / test_result.testsRun)
        
        # Add failed tests
        for test, traceback in test_result.failures:
            self.result.add_test_result(str(test), 'FAIL', 0, traceback)
        
        # Add error tests
        for test, traceback in test_result.errors:
            self.result.add_test_result(str(test), 'ERROR', 0, traceback)
        
        # Add skipped tests
        for test, reason in test_result.skipped:
            self.result.add_test_result(str(test), 'SKIP', 0, reason)
    
    def _print_final_summary(self):
        """Print final test summary."""
        summary = self.result.get_summary()
        
        print("\n" + "=" * 60)
        print("📊 FINAL TEST SUMMARY")
        print("=" * 60)
        
        print(f"🔢 Total Tests: {summary['total_tests']}")
        print(f"✅ Passed: {summary['passed']}")
        print(f"❌ Failed: {summary['failed']}")
        print(f"💥 Errors: {summary['errors']}")
        print(f"⏭️  Skipped: {summary['skipped']}")
        print(f"📈 Success Rate: {summary['success_rate']}%")
        print(f"⏱️  Total Duration: {summary['duration']}s")
        
        # Print status indicator
        if summary['failed'] == 0 and summary['errors'] == 0:
            print("\n🎉 ALL TESTS PASSED! 🎉")
        else:
            print(f"\n⚠️  {summary['failed'] + summary['errors']} TESTS FAILED")
        
        # Print detailed failures if any
        if summary['failed'] > 0 or summary['errors'] > 0:
            print("\n📋 DETAILED FAILURE REPORT:")
            print("-" * 40)
            
            for test_result in summary['test_results']:
                if test_result['status'] in ['FAIL', 'ERROR']:
                    print(f"\n❌ {test_result['test_name']}")
                    print(f"   Status: {test_result['status']}")
                    if test_result['error_msg']:
                        # Print first few lines of error
                        error_lines = test_result['error_msg'].split('\n')[:5]
                        for line in error_lines:
                            print(f"   {line}")
                        if len(test_result['error_msg'].split('\n')) > 5:
                            print("   ...")
    
    def save_results(self, output_file='test_results.json'):
        """Save test results to file."""
        summary = self.result.get_summary()
        
        with open(output_file, 'w') as f:
            json.dump(summary, f, indent=2)
        
        print(f"\n💾 Test results saved to {output_file}")


def run_performance_tests():
    """Run performance tests."""
    print("\n🚀 Running Performance Tests")
    print("-" * 30)
    
    # Import performance test modules if they exist
    try:
        import tests.test_performance
        loader = unittest.TestLoader()
        suite = loader.loadTestsFromModule(tests.test_performance)
        runner = unittest.TextTestRunner(verbosity=2)
        result = runner.run(suite)
        return result
    except ImportError:
        print("⚠️  No performance tests found")
        return None


def run_integration_tests():
    """Run integration tests."""
    print("\n🔗 Running Integration Tests")
    print("-" * 30)
    
    # Run integration tests that test multiple components together
    try:
        # These would be more comprehensive integration tests
        print("✅ Integration tests would run here")
        return True
    except Exception as e:
        print(f"❌ Integration tests failed: {e}")
        return False


def cleanup_test_environment():
    """Clean up test environment."""
    print("\n🧹 Cleaning up test environment...")
    
    # Clean up test directories
    import shutil
    
    test_dirs = [
        TEST_CONFIG['workspace_dir'],
        TEST_CONFIG['temp_dir']
    ]
    
    for test_dir in test_dirs:
        if test_dir.exists():
            try:
                shutil.rmtree(test_dir)
                print(f"✅ Cleaned up {test_dir}")
            except Exception as e:
                print(f"⚠️  Could not clean up {test_dir}: {e}")


def main():
    """Main test runner function."""
    print("🧪 Enhanced AI Agent - Test Suite Runner")
    print("Starting comprehensive testing...")
    
    # Setup test environment
    print("\n🔧 Setting up test environment...")
    for dir_path in [TEST_CONFIG['workspace_dir'], TEST_CONFIG['temp_dir']]:
        dir_path.mkdir(exist_ok=True)
    
    try:
        # Run main test suite
        runner = EnhancedTestRunner(verbosity=2)
        result = runner.discover_and_run_tests()
        
        # Save results
        runner.save_results()
        
        # Run additional test types
        run_performance_tests()
        run_integration_tests()
        
        # Final summary
        summary = result.get_summary()
        success = summary['failed'] == 0 and summary['errors'] == 0
        
        print(f"\n{'🎉 SUCCESS' if success else '❌ FAILURE'}: Test suite completed")
        
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\n⚠️  Test run interrupted by user")
        return 1
    except Exception as e:
        print(f"\n💥 Unexpected error during test run: {e}")
        return 1
    finally:
        # Always clean up
        cleanup_test_environment()


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
