#!/usr/bin/env python3
"""
Comprehensive Integration Verification for Enhanced AI Agent
Verifies that all components are properly integrated and working together.
"""

import sys
import json
import time
from pathlib import Path
from typing import Dict, List, Any

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from agent import EnhancedAgent
    from models import ModelManager
    from conversation import ConversationManager
    from core.fullstack_project_manager import ProjectType
except ImportError as e:
    print(f"❌ Import Error: {e}")
    print("Please ensure all dependencies are installed and the project structure is correct.")
    sys.exit(1)


class IntegrationVerifier:
    """Comprehensive integration verification system."""
    
    def __init__(self):
        self.test_workspace = Path.cwd() / 'integration_test_workspace'
        self.test_workspace.mkdir(exist_ok=True)
        self.results = []
        self.agent = None
    
    def run_verification(self) -> Dict[str, Any]:
        """Run comprehensive integration verification."""
        print("🔍 Enhanced AI Agent - Integration Verification")
        print("=" * 60)
        
        verification_steps = [
            ("Agent Initialization", self._verify_agent_initialization),
            ("Tool Registration", self._verify_tool_registration),
            ("File Operations", self._verify_file_operations),
            ("Advanced Tools", self._verify_advanced_tools),
            ("Workflow Management", self._verify_workflow_management),
            ("Full-Stack Projects", self._verify_fullstack_projects),
            ("Code Analysis", self._verify_code_analysis),
            ("Terminal Management", self._verify_terminal_management),
            ("Project Templates", self._verify_project_templates),
            ("Error Handling", self._verify_error_handling),
            ("Performance", self._verify_performance),
            ("Concurrent Operations", self._verify_concurrent_operations)
        ]
        
        passed = 0
        total = len(verification_steps)
        
        for step_name, step_function in verification_steps:
            print(f"\n🔧 Verifying: {step_name}")
            print("-" * 40)
            
            try:
                start_time = time.time()
                result = step_function()
                duration = time.time() - start_time
                
                if result['success']:
                    print(f"✅ {step_name}: PASSED ({duration:.2f}s)")
                    passed += 1
                else:
                    print(f"❌ {step_name}: FAILED - {result.get('error', 'Unknown error')}")
                
                self.results.append({
                    'step': step_name,
                    'success': result['success'],
                    'duration': duration,
                    'details': result
                })
                
            except Exception as e:
                print(f"💥 {step_name}: ERROR - {e}")
                self.results.append({
                    'step': step_name,
                    'success': False,
                    'duration': 0,
                    'error': str(e)
                })
        
        # Final summary
        success_rate = (passed / total) * 100
        print(f"\n📊 INTEGRATION VERIFICATION SUMMARY")
        print("=" * 60)
        print(f"✅ Passed: {passed}/{total} ({success_rate:.1f}%)")
        print(f"❌ Failed: {total - passed}/{total}")
        
        if success_rate >= 90:
            print("\n🎉 EXCELLENT: Agent is fully integrated and operational!")
        elif success_rate >= 75:
            print("\n👍 GOOD: Agent is mostly integrated with minor issues")
        elif success_rate >= 50:
            print("\n⚠️  FAIR: Agent has significant integration issues")
        else:
            print("\n❌ POOR: Agent has major integration problems")
        
        return {
            'success_rate': success_rate,
            'passed': passed,
            'total': total,
            'results': self.results
        }
    
    def _verify_agent_initialization(self) -> Dict[str, Any]:
        """Verify agent initialization."""
        try:
            # Mock dependencies
            from unittest.mock import Mock
            mock_model_manager = Mock(spec=ModelManager)
            mock_conversation_manager = Mock(spec=ConversationManager)
            
            # Initialize agent
            self.agent = EnhancedAgent(
                model_manager=mock_model_manager,
                conversation_manager=mock_conversation_manager,
                workspace_dir=self.test_workspace
            )
            
            # Verify components
            checks = [
                ('tool_manager', self.agent.tool_manager),
                ('workflow_manager', self.agent.workflow_manager),
                ('fullstack_manager', self.agent.fullstack_manager),
                ('thread_manager', self.agent.thread_manager),
                ('file_tool', self.agent.file_tool),
                ('shell_tool', self.agent.shell_tool)
            ]
            
            for name, component in checks:
                if component is None:
                    return {'success': False, 'error': f'{name} not initialized'}
            
            return {'success': True, 'components_initialized': len(checks)}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _verify_tool_registration(self) -> Dict[str, Any]:
        """Verify all tools are properly registered."""
        try:
            if not self.agent:
                return {'success': False, 'error': 'Agent not initialized'}
            
            tools = self.agent.tool_manager.tools
            
            # Essential tools that should be registered
            essential_tools = [
                'shell_execute', 'file_read', 'file_write', 'file_delete',
                'code_execute', 'web_search', 'codebase_analyze',
                'advanced_grep', 'create_project', 'generate_dockerfile',
                'create_fullstack_project', 'analyze_code_complexity'
            ]
            
            missing_tools = []
            for tool_name in essential_tools:
                if tool_name not in tools:
                    missing_tools.append(tool_name)
            
            if missing_tools:
                return {
                    'success': False, 
                    'error': f'Missing tools: {missing_tools}',
                    'registered_tools': len(tools),
                    'missing_tools': missing_tools
                }
            
            return {
                'success': True, 
                'registered_tools': len(tools),
                'essential_tools_found': len(essential_tools)
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _verify_file_operations(self) -> Dict[str, Any]:
        """Verify file operations work correctly."""
        try:
            test_file = 'integration_test.txt'
            test_content = 'Hello, Integration Test!'
            
            # Test file write
            result = self.agent._file_write(test_file, test_content)
            if 'error' in result.lower():
                return {'success': False, 'error': f'File write failed: {result}'}
            
            # Test file read
            result = self.agent._file_read(test_file)
            if test_content not in result:
                return {'success': False, 'error': 'File read failed or content mismatch'}
            
            # Test file delete
            result = self.agent._file_delete(test_file)
            if 'error' in result.lower():
                return {'success': False, 'error': f'File delete failed: {result}'}
            
            return {'success': True, 'operations_tested': 3}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _verify_advanced_tools(self) -> Dict[str, Any]:
        """Verify advanced tools functionality."""
        try:
            # Create test files for grep
            (self.test_workspace / 'test1.py').write_text('def hello(): pass')
            (self.test_workspace / 'test2.py').write_text('class World: pass')
            
            # Test advanced grep
            result = self.agent._advanced_grep('def', '*.py')
            result_data = json.loads(result)
            
            if not result_data.get('success'):
                return {'success': False, 'error': 'Advanced grep failed'}
            
            # Test backup creation
            result = self.agent._create_backup('test1.py')
            result_data = json.loads(result)
            
            if not result_data.get('success'):
                return {'success': False, 'error': 'Backup creation failed'}
            
            return {'success': True, 'advanced_tools_tested': 2}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _verify_workflow_management(self) -> Dict[str, Any]:
        """Verify workflow management functionality."""
        try:
            user_input = "Create a Python web application"
            
            # Test workflow plan creation
            result = self.agent._create_workflow_plan(user_input)
            result_data = json.loads(result)
            
            if not result_data.get('success'):
                return {'success': False, 'error': 'Workflow plan creation failed'}
            
            plan_id = result_data.get('plan_id')
            if not plan_id:
                return {'success': False, 'error': 'No plan ID returned'}
            
            # Test workflow progress
            result = self.agent._get_workflow_progress(plan_id)
            progress_data = json.loads(result)
            
            if 'error' in progress_data:
                return {'success': False, 'error': 'Workflow progress check failed'}
            
            return {'success': True, 'workflow_features_tested': 2}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _verify_fullstack_projects(self) -> Dict[str, Any]:
        """Verify full-stack project creation."""
        try:
            project_name = 'integration_test_project'
            project_type = 'python_web'
            
            # Test project creation
            result = self.agent._create_fullstack_project(project_name, project_type)
            result_data = json.loads(result)
            
            if not result_data.get('success'):
                return {'success': False, 'error': f'Project creation failed: {result_data.get("error")}'}
            
            # Verify project directory exists
            project_path = self.test_workspace / project_name
            if not project_path.exists():
                return {'success': False, 'error': 'Project directory not created'}
            
            return {'success': True, 'project_created': project_name}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _verify_code_analysis(self) -> Dict[str, Any]:
        """Verify code analysis functionality."""
        try:
            # Create a test Python file
            test_file = self.test_workspace / 'analysis_test.py'
            test_code = '''
def function1():
    """A test function."""
    return 42

class TestClass:
    def method1(self):
        return "test"
'''
            test_file.write_text(test_code)
            
            # Test code complexity analysis
            result = self.agent._analyze_code_complexity(str(test_file.relative_to(self.test_workspace)))
            result_data = json.loads(result)
            
            if not result_data.get('success'):
                return {'success': False, 'error': 'Code analysis failed'}
            
            analysis = result_data.get('analysis', {})
            if analysis.get('function_count', 0) == 0:
                return {'success': False, 'error': 'Function count not detected'}
            
            return {'success': True, 'analysis_features': len(analysis)}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _verify_terminal_management(self) -> Dict[str, Any]:
        """Verify terminal management functionality."""
        try:
            session_id = 'integration_test_session'
            
            # Test session creation
            result = self.agent._create_terminal_session(session_id)
            result_data = json.loads(result)
            
            if not result_data.get('success'):
                return {'success': False, 'error': 'Terminal session creation failed'}
            
            # Test session closure
            result = self.agent._close_terminal_session(session_id)
            result_data = json.loads(result)
            
            if not result_data.get('success'):
                return {'success': False, 'error': 'Terminal session closure failed'}
            
            return {'success': True, 'terminal_operations': 2}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _verify_project_templates(self) -> Dict[str, Any]:
        """Verify project template functionality."""
        try:
            # Test different project types
            project_types = ['python', 'javascript', 'react']
            successful_projects = 0
            
            for i, template in enumerate(project_types):
                project_name = f'template_test_{i}'
                result = self.agent._create_project(project_name, template)
                result_data = json.loads(result)
                
                if result_data.get('success'):
                    successful_projects += 1
            
            if successful_projects == 0:
                return {'success': False, 'error': 'No project templates working'}
            
            return {
                'success': True, 
                'templates_tested': len(project_types),
                'successful_projects': successful_projects
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _verify_error_handling(self) -> Dict[str, Any]:
        """Verify error handling mechanisms."""
        try:
            # Test file operation with invalid path
            result = self.agent._file_read('/nonexistent/path/file.txt')
            if 'error' not in result.lower():
                return {'success': False, 'error': 'Error handling not working for invalid file'}
            
            # Test project creation with invalid type
            result = self.agent._create_fullstack_project('test', 'invalid_type')
            if 'error' not in result.lower():
                return {'success': False, 'error': 'Error handling not working for invalid project type'}
            
            return {'success': True, 'error_cases_tested': 2}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _verify_performance(self) -> Dict[str, Any]:
        """Verify performance characteristics."""
        try:
            # Test response time for basic operations
            start_time = time.time()
            
            # Perform several operations
            self.agent._file_write('perf_test.txt', 'test content')
            self.agent._file_read('perf_test.txt')
            self.agent._file_delete('perf_test.txt')
            
            duration = time.time() - start_time
            
            # Should complete basic operations quickly
            if duration > 5.0:  # 5 seconds threshold
                return {'success': False, 'error': f'Operations too slow: {duration:.2f}s'}
            
            return {'success': True, 'duration': duration}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _verify_concurrent_operations(self) -> Dict[str, Any]:
        """Verify concurrent operations handling."""
        try:
            import threading
            
            results = []
            errors = []
            
            def create_file(index):
                try:
                    result = self.agent._file_write(f'concurrent_{index}.txt', f'Content {index}')
                    results.append(result)
                except Exception as e:
                    errors.append(str(e))
            
            # Create multiple threads
            threads = []
            for i in range(3):  # Reduced number for integration test
                thread = threading.Thread(target=create_file, args=(i,))
                threads.append(thread)
                thread.start()
            
            # Wait for completion
            for thread in threads:
                thread.join()
            
            if errors:
                return {'success': False, 'error': f'Concurrent operations failed: {errors}'}
            
            if len(results) != 3:
                return {'success': False, 'error': f'Expected 3 results, got {len(results)}'}
            
            return {'success': True, 'concurrent_operations': len(results)}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def cleanup(self):
        """Clean up test environment."""
        try:
            import shutil
            if self.test_workspace.exists():
                shutil.rmtree(self.test_workspace)
            print("✅ Test workspace cleaned up")
        except Exception as e:
            print(f"⚠️  Could not clean up test workspace: {e}")


def main():
    """Main verification function."""
    verifier = IntegrationVerifier()
    
    try:
        results = verifier.run_verification()
        
        # Save results
        with open('integration_results.json', 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"\n💾 Results saved to integration_results.json")
        
        # Return appropriate exit code
        return 0 if results['success_rate'] >= 75 else 1
        
    except KeyboardInterrupt:
        print("\n⚠️  Verification interrupted by user")
        return 1
    except Exception as e:
        print(f"\n💥 Unexpected error during verification: {e}")
        return 1
    finally:
        verifier.cleanup()


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
